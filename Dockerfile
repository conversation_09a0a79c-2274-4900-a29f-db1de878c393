FROM denoland/deno:1.40.2

# 设置工作目录
WORKDIR /app

# 复制源代码
COPY openai_function_call_proxy.ts .
COPY example_usage.ts .
COPY PROXY_README.md .

# 预缓存依赖
RUN deno cache openai_function_call_proxy.ts
RUN deno cache example_usage.ts

# 设置默认环境变量
ENV PORT=8000
ENV UPSTREAM_URL=https://api.openai.com

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["deno", "run", "--allow-net", "--allow-env", "openai_function_call_proxy.ts"]
