{"extends": "@electron-toolkit/tsconfig/tsconfig.web.json", "include": ["src/renderer/src/**/*", "src/preload/*.d.ts", "local/src/renderer/**/*", "packages/shared/**/*"], "compilerOptions": {"composite": true, "jsx": "react-jsx", "baseUrl": ".", "moduleResolution": "bundler", "paths": {"@renderer/*": ["src/renderer/src/*"], "@shared/*": ["packages/shared/*"], "@types": ["src/renderer/src/types/index.ts"]}}}