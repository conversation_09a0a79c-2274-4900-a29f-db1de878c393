<!doctype html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="initial-scale=1, width=device-width" />
    <meta http-equiv="Content-Security-Policy"
        content="default-src 'self'; connect-src blob: *; script-src 'self' 'unsafe-eval' *; worker-src 'self' blob:; style-src 'self' 'unsafe-inline' *; font-src 'self' data: *; img-src 'self' data: file: * blob:; frame-src * file:" />
    <title>Cherry Studio Selection Toolbar</title>

</head>

<body>
    <div id="root"></div>
    <script type="module" src="/src/windows/selection/toolbar/entryPoint.tsx"></script>
    <style>
        html {
            margin: 0;
        }

        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            width: 100vw;
            height: 100vh;

            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        #root {
            margin: 0;
            padding: 0;
            width: max-content !important;
            height: fit-content !important;
        }
    </style>
</body>

</html>