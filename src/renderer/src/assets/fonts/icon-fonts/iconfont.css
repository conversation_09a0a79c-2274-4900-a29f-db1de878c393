@font-face {
  font-family: 'iconfont'; /* Project id 4753420 */
  src: url('iconfont.woff2?t=1742184675192') format('woff2');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-obsidian:before {
  content: '\e677';
}

.icon-notion:before {
  content: '\e690';
}

.icon-thinking:before {
  content: '\e65b';
}

.icon-at:before {
  content: '\e623';
}

.icon-icon-adaptive-width:before {
  content: '\e87a';
}

.icon-at1:before {
  content: '\e630';
}

.icon-ai-model:before {
  content: '\e827';
}

.icon-ai-model1:before {
  content: '\ec09';
}

.icon-gridlines:before {
  content: '\e942';
}

.icon-inbox:before {
  content: '\e869';
}

.icon-business-smart-assistant:before {
  content: '\e601';
}

.icon-copy:before {
  content: '\e6ae';
}

.icon-ic_send:before {
  content: '\e795';
}

.icon-dark1:before {
  content: '\e72f';
}

.icon-theme-light:before {
  content: '\e6b7';
}

.icon-translate_line:before {
  content: '\e7de';
}

.icon-history:before {
  content: '\e758';
}

.icon-hide-sidebar:before {
  content: '\e8eb';
}

.icon-show-sidebar:before {
  content: '\e944';
}

.icon-appstore:before {
  content: '\e792';
}

.icon-chat:before {
  content: '\e615';
}

.icon-setting:before {
  content: '\e78e';
}
