@use './font.scss';

html {
  font-family: var(--font-family);
}

:root {
  --color-selection-toolbar-background: rgba(20, 20, 20, 0.95);
  --color-selection-toolbar-border: rgba(55, 55, 55, 0.5);
  --color-selection-toolbar-shadow: rgba(50, 50, 50, 0.3);

  --color-selection-toolbar-text: rgba(255, 255, 245, 0.9);
  --color-selection-toolbar-hover-bg: #222222;

  --color-primary: #00b96b;
  --color-error: #f44336;
}

[theme-mode='light'] {
  --color-selection-toolbar-background: rgba(245, 245, 245, 0.95);
  --color-selection-toolbar-border: rgba(200, 200, 200, 0.5);
  --color-selection-toolbar-shadow: rgba(50, 50, 50, 0.3);

  --color-selection-toolbar-text: rgba(0, 0, 0, 1);
  --color-selection-toolbar-hover-bg: rgba(0, 0, 0, 0.04);
}
