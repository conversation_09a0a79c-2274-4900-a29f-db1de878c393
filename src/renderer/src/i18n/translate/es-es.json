{"translation": {"agents": {"add.button": "Agregar al asistente", "add.knowledge_base": "Base de conocimiento", "add.knowledge_base.placeholder": "Seleccionar base de conocimiento", "add.name": "Nombre", "add.name.placeholder": "Ingrese el nombre", "add.prompt": "Palabra clave", "add.prompt.placeholder": "Ingrese la palabra clave", "add.title": "Crear agente inteligente", "delete.popup.content": "¿Está seguro de que desea eliminar este agente inteligente?", "edit.message.add.title": "Agregar", "edit.message.assistant.placeholder": "Ingrese el mensaje del asistente", "edit.message.assistant.title": "<PERSON><PERSON><PERSON>", "edit.message.empty.content": "El contenido de la sesión de chat no puede estar vacío", "edit.message.group.title": "Grupo de mensajes", "edit.message.title": "<PERSON><PERSON>je predeterminado", "edit.message.user.placeholder": "Ingrese el mensaje del usuario", "edit.message.user.title": "Usuario", "edit.model.select.title": "Seleccionar modelo", "edit.settings.hide_preset_messages": "<PERSON><PERSON>ltar mensajes predeterminados", "edit.title": "Editar agente inteligente", "manage.title": "Administrar agentes inteligentes", "my_agents": "Mis agentes inteligentes", "search.no_results": "No se encontraron agentes relacionados", "sorting.title": "Ordenar", "tag.agent": "<PERSON><PERSON>", "tag.default": "Predeterminado", "tag.new": "Nuevo", "tag.system": "Sistema", "title": "<PERSON><PERSON>", "import": {"type": {"url": "URL", "file": "Archivo"}, "error": {"url_required": "Por favor, introduzca la URL", "fetch_failed": "Error al obtener los datos de la URL", "invalid_format": "Formato de proxy no válido: faltan campos obligatorios"}, "title": "Importar desde el exterior", "url_placeholder": "Ingrese la URL JSON", "select_file": "Seleccionar archivo", "button": "Importar", "file_filter": "Archivos JSON"}, "export": {"agent": "Exportar Agente"}}, "assistants": {"abbr": "<PERSON><PERSON><PERSON>", "clear.content": "Vaciar el tema eliminará todos los temas y archivos del asistente. ¿Está seguro de que desea continuar?", "clear.title": "<PERSON><PERSON><PERSON>", "copy.title": "<PERSON><PERSON><PERSON>", "delete.content": "Eliminar el asistente borrará todos los temas y archivos asociados. ¿Está seguro de que desea continuar?", "delete.title": "<PERSON><PERSON><PERSON>", "edit.title": "<PERSON><PERSON>", "save.success": "Guardado exitosamente", "save.title": "Guardar en Agente Inteligente", "search": "Buscar As<PERSON>ente", "settings.default_model": "<PERSON><PERSON>determinado", "settings.knowledge_base": "Configuración de Base de Conocimientos", "settings.model": "Configuración de Modelo", "settings.preset_messages": "Mensajes <PERSON>esta<PERSON>cid<PERSON>", "settings.prompt": "Configuración de Palabras Clave", "settings.reasoning_effort": "Longitud de Cadena de Razonamiento", "settings.reasoning_effort.high": "Largo", "settings.reasoning_effort.low": "Corto", "settings.reasoning_effort.medium": "Medio", "settings.reasoning_effort.off": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "settings.regular_phrases": {"title": "Frases comunes", "add": "Agregar frase", "edit": "<PERSON>ar frase", "delete": "Eliminar frase", "deleteConfirm": "¿Está seguro de que desea eliminar esta frase?", "titleLabel": "<PERSON><PERSON><PERSON><PERSON>", "titlePlaceholder": "Ingrese el título", "contentLabel": "Contenido", "contentPlaceholder": "Por favor, introduzca el contenido de la frase. Puede usar variables y luego presionar Tab para navegar rápidamente a las variables y modificarlas. Por ejemplo: \\nAyúdame a planificar una ruta desde ${from} hasta ${to}, y luego envíala a ${email}."}, "settings.title": "Configuración del Asistente", "icon.type": "Ícono del Asistente", "settings.mcp": "Servidor MCP", "settings.mcp.enableFirst": "Habilite este servidor en la configuración de MCP primero", "settings.mcp.title": "Configuración MCP", "settings.mcp.noServersAvailable": "No hay servidores MCP disponibles. Agregue un servidor en la configuración", "settings.mcp.description": "Servidor MCP habilitado por defecto", "settings.knowledge_base.recognition.tip": "El agente utilizará la capacidad del modelo grande para el reconocimiento de intenciones y decidirá si necesita invocar la base de conocimientos para responder. Esta función dependerá de las capacidades del modelo", "settings.knowledge_base.recognition": "Invocar base de conocimientos", "settings.knowledge_base.recognition.off": "Búsqueda forzada", "settings.knowledge_base.recognition.on": "Reconocimiento de intención", "settings.reasoning_effort.default": "Por defecto", "settings.more": "Configuración del Asistente"}, "auth": {"error": "Falló la obtención automática de la clave, por favor obténla manualmente", "get_key": "Obtener", "get_key_success": "Obtención automática de la clave exitosa", "login": "In<PERSON><PERSON>", "oauth_button": "Iniciar <PERSON><PERSON><PERSON> con {{provider}}"}, "backup": {"confirm": "¿Está seguro de que desea realizar una copia de seguridad de los datos?", "confirm.button": "Seleccionar ubicación de copia de seguridad", "confirm.file_checkbox": "El tamaño del archivo es {{size}}, ¿desea elegir el archivo de copia de seguridad?", "content": "Realizar una copia de seguridad de todos los datos, incluyendo registros de chat, configuraciones, bases de conocimiento y todos los demás datos. Tenga en cuenta que el proceso de copia de seguridad puede llevar algún tiempo, gracias por su paciencia.", "progress": {"completed": "Copia de seguridad completada", "compressing": "Comprimiendo archivos...", "copying_files": "Copiando archivos... {{progress}}%", "preparing": "Preparando copia de seguridad...", "title": "Progreso de la copia de seguridad", "writing_data": "Escribiendo datos..."}, "title": "Copia de Seguridad de Datos"}, "button": {"add": "Agregar", "added": "<PERSON><PERSON><PERSON><PERSON>", "collapse": "Colapsar", "manage": "Administrar", "select_model": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "show.all": "<PERSON><PERSON>", "update_available": "Hay Actualizaciones Disponibles"}, "chat": {"add.assistant.title": "Ag<PERSON>gar as<PERSON>", "artifacts.button.download": "<PERSON><PERSON><PERSON>", "artifacts.button.openExternal": "Abrir en navegador externo", "artifacts.button.preview": "Vista previa", "artifacts.preview.openExternal.error.content": "Error al abrir en navegador externo", "assistant.search.placeholder": "Buscar", "deeply_thought": "Profundamente pensado (tom<PERSON> {{secounds}} segundos)", "default.description": "<PERSON><PERSON>, soy el asistente predeterminado. Puedes comenzar a conversar conmigo de inmediato.", "default.name": "<PERSON><PERSON><PERSON> predeterminado", "default.topic.name": "<PERSON><PERSON> predeterminado", "input.auto_resize": "Ajuste automático de altura", "input.clear": "<PERSON><PERSON><PERSON> mensajes {{Command}}", "input.clear.content": "¿Estás seguro de que quieres eliminar todos los mensajes de la sesión actual?", "input.clear.title": "Lim<PERSON><PERSON>", "input.collapse": "Colapsar", "input.context_count.tip": "Número de contextos / Número máximo de contextos", "input.estimated_tokens.tip": "Número estimado de tokens", "input.expand": "Expandir", "input.file_not_supported": "El modelo no admite este tipo de archivo", "input.knowledge_base": "Base de conocimientos", "input.new.context": "Limpiar contexto {{Command}}", "input.new_topic": "Nuevo tema {{Command}}", "input.pause": "Pausar", "input.placeholder": "Escribe aquí tu mensaje...", "input.send": "Enviar", "input.settings": "Configuración", "input.topics": "<PERSON><PERSON>", "input.translate": "Traducir a {{target_language}}", "input.upload": "Subir imagen o documento", "input.upload.document": "Subir documento (el modelo no admite imágenes)", "input.web_search": "Habilitar búsqueda web", "input.web_search.button.ok": "Ir a configuración", "input.web_search.enable": "Habilitar búsqueda web", "input.web_search.enable_content": "Primero verifica la conectividad de la búsqueda web en la configuración", "message.new.branch": "Rama nueva", "message.new.branch.created": "Nueva rama creada", "message.new.context": "Limpiar contexto", "message.quote": "Citar", "message.regenerate.model": "Cambiar modelo", "message.useful": "<PERSON><PERSON>", "navigation": {"first": "Ya es el primer mensaje", "last": "Ya es el último mensaje", "next": "Siguiente mensaje", "prev": "<PERSON><PERSON><PERSON>", "top": "Volver arriba", "bottom": "Volver abajo", "close": "<PERSON><PERSON><PERSON>", "history": "Historial de chat"}, "resend": "Reenviar", "save": "Guardar", "settings.code_collapsible": "Bloques de código plegables", "settings.code_wrappable": "Bloques de código reemplazables", "settings.context_count": "Número de contextos", "settings.context_count.tip": "Número de mensajes que se deben mantener en el contexto. Cuanto mayor sea el valor, más largo será el contexto y más tokens se consumirán. Para una conversación normal, se sugiere un valor entre 5-10", "settings.max": "Sin límite", "settings.max_tokens": "Habilitar límite de longitud del mensaje", "settings.max_tokens.confirm": "Habilitar límite de longitud del mensaje", "settings.max_tokens.confirm_content": "Al habilitar el límite de longitud del mensaje, se establece el número máximo de tokens por interacción, lo que afectará la longitud del resultado devuelto. Debe ajustarse según las limitaciones del contexto del modelo, de lo contrario se producirá un error", "settings.max_tokens.tip": "Número máximo de tokens por interacción, lo que afectará la longitud del resultado devuelto. Debe ajustarse según las limitaciones del contexto del modelo, de lo contrario se producirá un error", "settings.reset": "Restablecer", "settings.set_as_default": "Aplicar a asistente predeterminado", "settings.show_line_numbers": "Mostrar números de línea", "settings.temperature": "Temperatura del modelo", "settings.temperature.tip": "Aleatoriedad en la generación de texto del modelo. Cuanto mayor sea el valor, más diversidad, creatividad y aleatoriedad tendrá la respuesta; si se establece en 0, responde basándose en hechos. Para una conversación diaria, se recomienda un valor de 0.7", "settings.thought_auto_collapse": "Plegado automático del contenido de pensamiento", "settings.thought_auto_collapse.tip": "El contenido de pensamiento se pliega automáticamente después de finalizar el pensamiento", "settings.top_p": "Top-P", "settings.top_p.tip": "Valor predeterminado es 1, cuanto menor sea el valor, el contenido generado por la IA será menos variado pero más fácil de entender; cuanto mayor sea el valor, el vocabulario y la variedad de la respuesta de la IA serán mayores", "suggestions.title": "Preguntas sugeridas", "thinking": "Pensando", "topics.auto_rename": "Generar nombre de tema", "topics.clear.title": "Lim<PERSON><PERSON>", "topics.copy.image": "Copiar como imagen", "topics.copy.md": "Copiar como Markdown", "topics.copy.title": "Copiar", "topics.delete.shortcut": "Mantén presionada {{key}} para eliminar directamente", "topics.edit.placeholder": "Introduce nuevo nombre", "topics.edit.title": "Editar nombre del tema", "topics.export.image": "Exportar como imagen", "topics.export.joplin": "Exportar a Jo<PERSON>lin", "topics.export.md": "Exportar como Markdown", "topics.export.notion": "Exportar a Notion", "topics.export.obsidian": "Exportar a Obsidian", "topics.export.obsidian_atributes": "Configurar atributos de nota", "topics.export.obsidian_btn": "Aceptar", "topics.export.obsidian_created": "Fecha de creación", "topics.export.obsidian_created_placeholder": "Selecciona la fecha de creación", "topics.export.obsidian_export_failed": "Exportación fallida", "topics.export.obsidian_export_success": "Exportación exitosa", "topics.export.obsidian_operate": "Modo de operación", "topics.export.obsidian_operate_append": "Agregar", "topics.export.obsidian_operate_new_or_overwrite": "<PERSON><PERSON>r nuevo (si existe, sobrescribir)", "topics.export.obsidian_operate_placeholder": "Selecciona el modo de operación", "topics.export.obsidian_operate_prepend": "Preponer", "topics.export.obsidian_source": "Fuente", "topics.export.obsidian_source_placeholder": "Introduce la fuente", "topics.export.obsidian_tags": "Etiquetas", "topics.export.obsidian_tags_placeholder": "Introduce etiquetas, múltiples etiquetas separadas por comas, Obsidian no admite números puros", "topics.export.obsidian_title": "<PERSON><PERSON><PERSON><PERSON>", "topics.export.obsidian_title_placeholder": "Introduce el título", "topics.export.obsidian_title_required": "El título no puede estar vacío", "topics.export.title": "Exportar", "topics.export.word": "Exportar como Word", "topics.export.yuque": "Exportar a Yuque", "topics.list": "Lista de temas", "topics.move_to": "Mover a", "topics.new": "Iniciar nueva conversación", "topics.pinned": "<PERSON><PERSON> tema", "topics.prompt": "Palabras clave del tema", "topics.prompt.edit.title": "Editar palabras clave del tema", "topics.prompt.tips": "Palabras clave del tema: proporcionar indicaciones adicionales para el tema actual", "topics.title": "<PERSON><PERSON>", "topics.unpinned": "<PERSON>uitar fija<PERSON>", "translate": "Traducir", "input.generate_image": "Generar imagen", "input.generate_image_not_supported": "El modelo no soporta la generación de imágenes", "history": {"assistant_node": "<PERSON><PERSON><PERSON>", "click_to_navigate": "Haga clic para ir al mensaje correspondiente", "coming_soon": "Próximamente: gráfico del flujo de chat", "no_messages": "No se encontraron mensajes", "start_conversation": "Inicie una conversación para ver el gráfico del flujo de chat", "title": "Historial de chat", "user_node": "Usuario", "view_full_content": "Ver contenido completo"}, "input.translating": "Traduciendo...", "input.thinking": "Pensando", "input.thinking.mode.default": "Predeterminado", "input.thinking.mode.default.tip": "El modelo determinará automáticamente la cantidad de tokens a pensar", "input.thinking.mode.custom": "Personalizado", "input.thinking.mode.custom.tip": "Número máximo de tokens que puede procesar el modelo. Debe tenerse en cuenta el límite del contexto del modelo, de lo contrario se generará un error", "input.thinking.budget_exceeds_max": "El presupuesto de pensamiento excede el número máximo de tokens", "input.upload.upload_from_local": "Subir archivo local...", "input.web_search.builtin": "Integrada en el modelo", "input.web_search.builtin.enabled_content": "Usar la función de búsqueda web integrada en el modelo", "input.web_search.builtin.disabled_content": "La búsqueda web no es compatible con este modelo actualmente", "input.web_search.no_web_search": "Sin búsqueda web", "input.web_search.no_web_search.description": "No activar la función de búsqueda web", "settings.code_cacheable": "Almacenamiento en caché de bloques de código", "settings.code_cacheable.tip": "El almacenamiento en caché de bloques de código puede reducir el tiempo de representación de bloques largos, pero aumenta el uso de memoria", "settings.code_cache_max_size": "Límite de ca<PERSON>é", "settings.code_cache_max_size.tip": "Límite de caracteres permitidos en caché (en miles), calculado según el código resaltado. La longitud del código resaltado suele ser mucho mayor que el texto plano.", "settings.code_cache_ttl": "Tiempo de vida de la caché", "settings.code_cache_ttl.tip": "Tiempo de expiración de la caché (en minutos)", "settings.code_cache_threshold": "Umbral de la caché", "settings.code_cache_threshold.tip": "Longitud mínima del código permitida para almacenarse en caché (en miles de caracteres), solo los bloques de código por encima de este umbral serán almacenados", "topics.export.md.reason": "Exportar como Markdown (incluye el razonamiento)", "topics.export.obsidian_vault": "Biblioteca", "topics.export.obsidian_vault_placeholder": "Seleccione el nombre de la biblioteca", "topics.export.obsidian_path": "<PERSON><PERSON>", "topics.export.obsidian_path_placeholder": "Seleccione una ruta", "topics.export.obsidian_no_vaults": "No se encontró ninguna biblioteca de Obsidian", "topics.export.obsidian_loading": "Cargando...", "topics.export.obsidian_fetch_error": "Error al obtener las bibliotecas de Obsidian", "topics.export.obsidian_fetch_folders_error": "Error al obtener la estructura de carpetas", "topics.export.obsidian_no_vault_selected": "Por favor seleccione primero una biblioteca", "topics.export.obsidian_select_vault_first": "Por favor seleccione una biblioteca primero", "topics.export.obsidian_root_directory": "Directorio raíz", "topics.export.siyuan": "Exportar a SiYuan Notes", "topics.export.wait_for_title_naming": "<PERSON><PERSON><PERSON> tí<PERSON>...", "topics.export.title_naming_success": "T<PERSON><PERSON>lo generado exitosamente", "topics.export.title_naming_failed": "Fallo al generar el título, usando el título predeterminado"}, "code_block": {"collapse": "Replegar", "disable_wrap": "Deshabilitar salto de línea", "enable_wrap": "Habilitar salto de línea", "expand": "Expandir"}, "common": {"add": "Agregar", "advanced_settings": "Configuración avanzada", "and": "y", "assistant": "Agente inteligente", "avatar": "Avatar", "back": "Atrás", "cancel": "<PERSON><PERSON><PERSON>", "chat": "Cha<PERSON>", "clear": "Limpiar", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "copied": "Copiado", "copy": "Copiar", "cut": "Cortar", "default": "Predeterminado", "delete": "Eliminar", "description": "Descripción", "docs": "Documentos", "download": "<PERSON><PERSON><PERSON>", "duplicate": "Duplicar", "edit": "<PERSON><PERSON>", "expand": "Expandir", "footnote": "Nota al pie", "footnotes": "Notas al pie", "fullscreen": "En modo pantalla completa, presione F11 para salir", "knowledge_base": "Base de conocimiento", "language": "Idioma", "model": "<PERSON><PERSON>", "models": "Modelos", "more": "Más", "name": "Nombre", "paste": "<PERSON><PERSON><PERSON>", "prompt": "Prompt", "provider": "<PERSON><PERSON><PERSON><PERSON>", "regenerate": "<PERSON><PERSON><PERSON>", "rename": "Renombrar", "reset": "Restablecer", "save": "Guardar", "search": "Buscar", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "topics": "<PERSON><PERSON>", "warning": "Advertencia", "you": "Usuario", "sort": {"pinyin": "Ordenar por pinyin", "pinyin.asc": "Ordenar por pinyin ascendente", "pinyin.desc": "Ordenar por pinyin descendente"}, "inspect": "Inspeccionar", "collapse": "Colapsar", "loading": "Cargando...", "reasoning_content": "Pensamiento profundo"}, "docs": {"title": "Documentación de Ayuda"}, "error": {"backup.file_format": "Formato de archivo de copia de seguridad incorrecto", "chat.response": "Ha ocurrido un error, si no ha configurado la clave API, vaya a Configuración > Proveedor de modelos para configurar la clave", "http": {"400": "Error en la solicitud, revise si los parámetros de la solicitud son correctos. Si modificó la configuración del modelo, restablezca a la configuración predeterminada", "401": "Fallo en la autenticación, revise si la clave API es correcta", "403": "Acceso prohibido, traduzca el mensaje de error específico para ver la causa o póngase en contacto con el proveedor de servicios para preguntar sobre la razón de la prohibición", "404": "El modelo no existe o la ruta de la solicitud está incorrecta", "429": "La tasa de solicitudes excede el límite, inténtelo de nuevo más tarde", "500": "Error del servidor, inténtelo de nuevo más tarde", "502": "Error de puerta de enlace, inténtelo de nuevo más tarde", "503": "Servicio no disponible, inténtelo de nuevo más tarde", "504": "Tiempo de espera de la puerta de enlace, inténtelo de nuevo más tarde"}, "model.exists": "El modelo ya existe", "no_api_key": "La clave API no está configurada", "provider_disabled": "El proveedor de modelos no está habilitado", "render": {"title": "<PERSON><PERSON><PERSON> render<PERSON>", "description": "Error al renderizar la fórmula, por favor, compruebe si el formato de la fórmula es correcto"}, "user_message_not_found": "No se pudo encontrar el mensaje original del usuario", "unknown": "Error descon<PERSON>", "pause_placeholder": "Interrumpido"}, "export": {"assistant": "<PERSON><PERSON><PERSON>", "attached_files": "Archivos adjuntos", "conversation_details": "Detalles de la conversación", "conversation_history": "Historial de la conversación", "created": "Fecha de creación", "last_updated": "Última actualización", "messages": "<PERSON><PERSON><PERSON><PERSON>", "user": "Usuario"}, "files": {"actions": "Acciones", "all": "Todos los archivos", "count": "Número de archivos", "created_at": "Fecha de creación", "delete": "Eliminar", "delete.content": "Eliminar el archivo eliminará todas las referencias del archivo en todos los mensajes. ¿Estás seguro de que quieres eliminar este archivo?", "delete.paintings.warning": "La imagen está incluida en un dibujo, por lo que temporalmente no se puede eliminar", "delete.title": "Eliminar archivo", "document": "Documento", "edit": "<PERSON><PERSON>", "file": "Archivo", "image": "Imagen", "name": "Nombre del archivo", "open": "Abrir", "size": "<PERSON><PERSON><PERSON>", "text": "Texto", "title": "Archivo", "type": "Tipo"}, "gpustack": {"keep_alive_time.description": "Tiempo que el modelo permanece en memoria (por defecto: 5 minutos)", "keep_alive_time.placeholder": "minutos", "keep_alive_time.title": "Tiempo de Actividad", "title": "GPUStack"}, "history": {"continue_chat": "Continuar chat", "locate.message": "Localizar <PERSON>", "search.messages": "Buscar todos los mensajes", "search.placeholder": "Buscar tema o mensaje...", "search.topics.empty": "No se encontraron temas relacionados, presione Enter para buscar todos los mensajes", "title": "Búsqueda de temas"}, "knowledge": {"add": {"title": "Agregar base de conocimientos"}, "add_directory": "Agregar directorio", "add_file": "Agregar archivo", "add_note": "Agregar nota", "add_sitemap": "Mapa del sitio", "add_url": "Agregar URL", "cancel_index": "<PERSON><PERSON><PERSON>", "chunk_overlap": "Superposición de fragmentos", "chunk_overlap_placeholder": "Valor predeterminado (no recomendado para modificar)", "chunk_overlap_tooltip": "La cantidad de contenido repetido entre bloques de texto adyacentes, asegurando que los fragmentos de texto divididos aún mantengan un contexto, mejorando el rendimiento general del modelo en textos largos", "chunk_size": "Tamaño de fragmento", "chunk_size_change_warning": "Las modificaciones del tamaño de fragmento y la superposición solo se aplican al nuevo contenido agregado", "chunk_size_placeholder": "Valor predeterminado (no recomendado para modificar)", "chunk_size_too_large": "El tamaño de fragmento no puede exceder el límite de contexto del modelo ({{max_context}})", "chunk_size_tooltip": "Divide el documento en fragmentos de este tamaño, no debe exceder el límite de contexto del modelo", "clear_selection": "Limpiar <PERSON>", "delete": "Eliminar", "delete_confirm": "¿Está seguro de querer eliminar esta base de conocimientos?", "directories": "Director<PERSON>", "directory_placeholder": "Ingrese la ruta del directorio", "document_count": "Número de fragmentos de documentos solicitados", "document_count_default": "Predeterminado", "document_count_help": "Más fragmentos de documentos solicitados significa más información adjunta, pero tamb<PERSON> consume más tokens", "drag_file": "Arrastre archivos aquí", "edit_remark": "Editar observación", "edit_remark_placeholder": "Ingrese el contenido de la observación", "empty": "Sin bases de conocimientos", "file_hint": "Formatos soportados: {{file_types}}", "index_all": "Indexar todo", "index_cancelled": "<PERSON><PERSON><PERSON> cancelado", "index_started": "Índice iniciado", "invalid_url": "URL inválida", "model_info": "Información del modelo", "no_bases": "Sin bases de conocimientos", "no_match": "No se encontraron coincidencias en la base de conocimientos", "no_provider": "El proveedor del modelo de la base de conocimientos está perdido, esta base de conocimientos ya no es compatible, por favor cree una nueva base de conocimientos", "not_set": "No configurado", "not_support": "El motor de base de datos de la base de conocimientos ha sido actualizado, esta base de conocimientos ya no es compatible, por favor cree una nueva base de conocimientos", "notes": "Notas", "notes_placeholder": "Ingrese información adicional o contexto para esta base de conocimientos...", "rename": "Renombrar", "search": "Buscar en la base de conocimientos", "search_placeholder": "Ingrese el contenido de la consulta", "settings": "Configuración de la base de conocimientos", "sitemap_placeholder": "Ingrese la URL del mapa del sitio", "sitemaps": "Sitios web", "source": "Fuente", "status": "Estado", "status_completed": "Completado", "status_failed": "Fallido", "status_new": "Nuevo", "status_pending": "Pendiente", "status_processing": "Procesando", "threshold": "Umbral de coincidencia", "threshold_placeholder": "No configurado", "threshold_too_large_or_small": "El umbral no puede ser mayor que 1 o menor que 0", "threshold_tooltip": "Se usa para medir la relevancia entre la pregunta del usuario y el contenido de la base de conocimientos (0-1)", "title": "Base de conocimientos", "topN": "Número de resultados devueltos", "topN__too_large_or_small": "El número de resultados devueltos no puede ser mayor que 100 o menor que 1", "topN_placeholder": "No configurado", "topN_tooltip": "Número de resultados coincidentes devueltos, un valor más alto significa más resultados coincidentes, pero también consume más tokens", "url_added": "URL agregada", "url_placeholder": "Ingrese la URL, múltiples URLs separadas por enter", "urls": "URLs", "dimensions": "Dimensión de incrustación", "dimensions_size_tooltip": "Tamaño de la dimensión de incrustación, cuanto mayor sea el valor, mayor será la dimensión de incrustación, pero también consumirá m<PERSON>s", "dimensions_size_placeholder": "Valor predeterminado (no recomendado modificar)", "dimensions_size_too_large": "La dimensión de incrustación no puede exceder el límite del contexto del modelo ({{max_context}})"}, "languages": {"arabic": "<PERSON><PERSON><PERSON>", "chinese": "Chino simplificado", "chinese-traditional": "Chino tradicional", "english": "Inglés", "french": "<PERSON><PERSON><PERSON><PERSON>", "german": "Alemán", "italian": "Italiano", "japanese": "Japonés", "korean": "<PERSON><PERSON>", "portuguese": "Portugués", "russian": "<PERSON><PERSON><PERSON>", "spanish": "Español"}, "lmstudio": {"keep_alive_time.description": "Tiempo que el modelo permanece en memoria después de la conversación (predeterminado: 5 minutos)", "keep_alive_time.placeholder": "minutos", "keep_alive_time.title": "Tiempo de Actividad", "title": "LM Studio"}, "mermaid": {"download": {"png": "Descargar PNG", "svg": "Descargar SVG"}, "resize": {"zoom-in": "Acercar", "zoom-out": "<PERSON><PERSON><PERSON>"}, "tabs": {"preview": "Vista previa", "source": "<PERSON><PERSON><PERSON> fuente"}, "title": "Gráfico Mermaid"}, "message": {"api.check.model.title": "Seleccione el modelo a verificar", "api.connection.failed": "Conexión fallida", "api.connection.success": "Conexión exitosa", "assistant.added.content": "Asist<PERSON> agregado con éxito", "attachments": {"pasted_image": "Imagen del portapapeles", "pasted_text": "Archivo del portapapeles"}, "backup.failed": "Backup fallido", "backup.start.success": "Inicio de backup", "backup.success": "Backup exitoso", "chat.completion.paused": "<PERSON><PERSON> pausado", "citations": "Citas", "copied": "Copiado", "copy.failed": "Copia fallida", "copy.success": "<PERSON><PERSON> exitosa", "error.chunk_overlap_too_large": "El solapamiento del fragmento no puede ser mayor que el tamaño del fragmento", "error.dimension_too_large": "La dimensión del contenido es demasiado grande", "error.enter.api.host": "Ingrese su dirección API", "error.enter.api.key": "Ingrese su clave API", "error.enter.model": "Seleccione un modelo", "error.enter.name": "Ingrese el nombre de la base de conocimiento", "error.fetchTopicName": "Error al nombrar el tema", "error.get_embedding_dimensions": "Fallo al obtener las dimensiones de incrustación", "error.invalid.api.host": "Dirección API inválida", "error.invalid.api.key": "Clave API inválida", "error.invalid.enter.model": "Seleccione un modelo", "error.invalid.proxy.url": "URL de proxy inválida", "error.invalid.webdav": "Configuración de WebDAV inválida", "error.joplin.export": "Error de exportación de Joplin, asegúrese de que Joplin esté en ejecución y verifique el estado de conexión o la configuración", "error.joplin.no_config": "No se ha configurado el token de autorización de Joplin o la URL", "error.markdown.export.preconf": "Error al exportar archivo Markdown a ruta predefinida", "error.markdown.export.specified": "Error al exportar archivo Markdown", "error.notion.export": "Error de exportación de Notion, verifique el estado de conexión y la configuración según la documentación", "error.notion.no_api_key": "No se ha configurado la clave API de Notion o la ID de la base de datos de Notion", "error.yuque.export": "Error de exportación de Yuque, verifique el estado de conexión y la configuración según la documentación", "error.yuque.no_config": "No se ha configurado el token de Yuque o la URL de la base de conocimiento", "group.delete.content": "Eliminar el mensaje del grupo eliminará la pregunta del usuario y todas las respuestas del asistente", "group.delete.title": "Eliminar mensaje del grupo", "ignore.knowledge.base": "Modo en línea activado, ignorando la base de conocimiento", "info.notion.block_reach_limit": "La conversación es demasiado larga, se está exportando por páginas a Notion", "loading.notion.exporting_progress": "Exportando a Notion ({{current}}/{{total}})...", "loading.notion.preparing": "Preparando para exportar a Notion...", "mention.title": "Cambiar modelo de respuesta", "message.code_style": "Estilo de <PERSON>", "message.delete.content": "¿Está seguro de querer eliminar este mensaje?", "message.delete.title": "Eliminar men<PERSON>", "message.multi_model_style": "Estilo de respuesta multi-modelo", "message.multi_model_style.fold": "Modo de etiquetas", "message.multi_model_style.fold.compress": "Cambiar a disposición compacta", "message.multi_model_style.fold.expand": "Cambiar a disposición expandida", "message.multi_model_style.grid": "Diseño de tarjetas", "message.multi_model_style.horizontal": "Disposición horizontal", "message.multi_model_style.vertical": "Pila vertical", "message.style": "Estilo de <PERSON>je", "message.style.bubble": "Burbuja", "message.style.plain": "Simple", "regenerate.confirm": "Regenerar sobrescribirá el mensaje actual", "reset.confirm.content": "¿Está seguro de querer restablecer todos los datos?", "reset.double.confirm.content": "Todos sus datos se perderán, si no tiene una copia de seguridad, no podrán ser recuperados, ¿desea continuar?", "reset.double.confirm.title": "¡¡Pérdida de datos!!", "restore.failed": "Restauración fallida", "restore.success": "Restauración exitosa", "save.success.title": "Guardado exitoso", "searching": "Buscando en línea...", "success.joplin.export": "Exportado con éxito a Jo<PERSON>lin", "success.markdown.export.preconf": "Archivo Markdown exportado con éxito a la ruta predefinida", "success.markdown.export.specified": "Archivo Markdown exportado con éxito", "success.notion.export": "Exportado con éxito a Notion", "success.yuque.export": "Exportado con éxito a Yuque", "switch.disabled": "Espere a que se complete la respuesta actual antes de realizar la operación", "tools": {"completed": "Completado", "invoking": "En llamada", "raw": "<PERSON><PERSON><PERSON>", "preview": "Vista previa", "error": "Se ha producido un error"}, "topic.added": "<PERSON><PERSON> agregado con éxito", "upgrade.success.button": "Reiniciar", "upgrade.success.content": "Reinicie para completar la actualización", "upgrade.success.title": "Actualización exitosa", "warn.notion.exporting": "Se está exportando a Notion, ¡no solicite nuevamente la exportación!", "warning.rate.limit": "<PERSON><PERSON><PERSON> demasiado frecuente, espere {{seconds}} segundos antes de intentarlo de nuevo", "agents": {"imported": "Importado con éxito", "import.error": "Error al importar"}, "citation": "{{count}} contenido citado", "error.invalid.nutstore": "Configuración de Nutstore no válida", "error.invalid.nutstore_token": "Token de Nutstore no válido", "processing": "Procesando...", "error.siyuan.export": "Error al exportar la nota de Siyuan, verifique el estado de la conexión y revise la configuración según la documentación", "error.siyuan.no_config": "No se ha configurado la dirección API o el token de Siyuan", "success.siyuan.export": "Exportado a Siyuan exitosamente", "warn.yuque.exporting": "Exportando <PERSON>, ¡no solicite la exportación nuevamente!", "warn.siyuan.exporting": "Exportando a Siyuan, ¡no solicite la exportación nuevamente!", "download.success": "<PERSON><PERSON><PERSON> exitosa", "download.failed": "<PERSON><PERSON><PERSON> fallida"}, "minapp": {"title": "Mini programa", "popup": {"refresh": "Actualizar", "close": "Cerrar la aplicación", "minimize": "Minimizar la aplicación", "devtools": "Herramientas de desarrollo", "openExternal": "Abrir en el navegador", "rightclick_copyurl": "Copiar URL con clic derecho", "open_link_external_on": "Actual: <PERSON><PERSON><PERSON> enlaces en el navegador", "open_link_external_off": "Actual: <PERSON><PERSON><PERSON> enlaces en ventana predeterminada"}, "sidebar": {"add": {"title": "Agregar a la barra lateral"}, "remove": {"title": "Eliminar de la barra lateral"}, "remove_custom": {"title": "Eliminar aplicación personalizada"}, "hide": {"title": "Ocultar"}, "close": {"title": "<PERSON><PERSON><PERSON>"}, "closeall": {"title": "<PERSON><PERSON><PERSON> todo"}}}, "miniwindow": {"clipboard": {"empty": "El portapapeles está vacío"}, "feature": {"chat": "Responder a esta pregunta", "explanation": "Explicación", "summary": "Resumen del contenido", "translate": "Traducción de texto"}, "footer": {"copy_last_message": "Presione C para copiar", "esc": "Presione ESC {{action}}", "esc_back": "Volver", "esc_close": "<PERSON><PERSON><PERSON> ventana", "backspace_clear": "Presione Retroceso para borrar"}, "input": {"placeholder": {"empty": "Pregunta a {{model}} para obtener ayuda...", "title": "¿Qué deseas hacer con el texto de abajo?"}}, "tooltip": {"pin": "Fijar en la parte superior"}}, "models": {"add_parameter": "Agregar parámetro", "all": "Todo", "custom_parameters": "Parámetros personalizados", "dimensions": "{{dimensions}} dimensiones", "edit": "<PERSON><PERSON>o", "embedding": "Inmersión", "embedding_model": "Modelo de inmersión", "embedding_model_tooltip": "Haga clic en el botón Administrar en Configuración-><PERSON>vic<PERSON> de modelos para agregar", "function_calling": "Llamada a función", "no_matches": "No hay modelos disponibles", "parameter_name": "Nombre del parámetro", "parameter_type": {"boolean": "<PERSON>or booleano", "json": "JSON", "number": "Número", "string": "Texto"}, "pinned": "<PERSON><PERSON><PERSON>", "rerank_model": "Modelo de reordenamiento", "rerank_model_support_provider": "Actualmente, el modelo de reordenamiento solo es compatible con algunos proveedores ({{provider}})", "rerank_model_tooltip": "Haga clic en el botón Administrar en Configuración-><PERSON>vic<PERSON> de modelos para agregar", "search": "Buscar modelo...", "stream_output": "Salida en flujo", "type": {"embedding": "Incrustación", "function_calling": "Llamada a función", "reasoning": "Razonamiento", "select": "Seleccionar tipo de modelo", "text": "Texto", "vision": "Imagen", "free": "<PERSON><PERSON><PERSON>", "rerank": "Reclasificar", "websearch": "Búsqueda en línea"}, "rerank_model_not_support_provider": "Actualmente, el modelo de reordenamiento no admite este proveedor ({{provider}})", "enable_tool_use": "Habilitar uso de herramientas"}, "navbar": {"expand": "Expandir cuadro de diálogo", "hide_sidebar": "Ocultar barra lateral", "show_sidebar": "Mostrar barra lateral"}, "ollama": {"keep_alive_time.description": "Tiempo que el modelo permanece en memoria después de la conversación (por defecto: 5 minutos)", "keep_alive_time.placeholder": "minutos", "keep_alive_time.title": "Tiempo de Actividad", "title": "Ollama"}, "paintings": {"button.delete.image": "Eliminar imagen", "button.delete.image.confirm": "¿Está seguro de que desea eliminar esta imagen?", "button.new.image": "Nueva imagen", "guidance_scale": "Escala de guía", "guidance_scale_tip": "Sin clasificador de guía. Controla la medida en que el modelo sigue la sugerencia al buscar imágenes relacionadas", "image.size": "Tam<PERSON><PERSON> de la imagen", "inference_steps": "Paso de inferencia", "inference_steps_tip": "Número de pasos de inferencia a realizar. Cuantos más pasos, mejor la calidad pero más tiempo tarda", "negative_prompt": "Prompt negativo", "negative_prompt_tip": "Describe lo que no quieres que aparezca en la imagen", "number_images": "Cantidad de imágenes generadas", "number_images_tip": "Número de imágenes generadas por vez (1-4)", "prompt_enhancement": "Mejora del prompt", "prompt_enhancement_tip": "Al activar esto, se reescribirá la sugerencia para una versión más detallada y adecuada para el modelo", "prompt_placeholder": "Describe la imagen que deseas crear, por ejemplo: un lago tranquilo, el sol poniente, con montañas lejanas", "regenerate.confirm": "Esto sobrescribirá las imágenes generadas, ¿desea continuar?", "seed": "<PERSON>lla aleatoria", "seed_tip": "La misma semilla y la misma sugerencia generarán imágenes similares", "title": "Imagen", "mode": {"generate": "Generar imagen", "edit": "<PERSON><PERSON>", "remix": "Mezclar", "upscale": "Ampliar"}, "generate": {"model_tip": "Versión del modelo: V2 es el modelo más reciente de la interfaz, V2A es un modelo rápido, V_1 es el modelo inicial y _TURBO es la versión acelerada", "number_images_tip": "Número de imágenes generadas a la vez", "seed_tip": "Controla la aleatoriedad en la generación de imágenes, útil para reproducir resultados idénticos", "negative_prompt_tip": "Describe elementos que no deseas en la imagen. Solo compatible con las versiones V_1, V_1_TURBO, V_2 y V_2_TURBO", "magic_prompt_option_tip": "Optimización inteligente de indicaciones para mejorar los resultados de generación", "style_type_tip": "Estilo de generación de imágenes, solo aplicable para la versión V_2 y posteriores"}, "edit": {"image_file": "Imagen editada", "model_tip": "La edición local solo es compatible con las versiones V_2 y V_2_TURBO", "number_images_tip": "Número de resultados de edición generados", "style_type_tip": "Estilo de la imagen editada, solo aplicable para la versión V_2 y posteriores", "seed_tip": "Controla la aleatoriedad de los resultados de edición", "magic_prompt_option_tip": "Optimización inteligente de las palabras clave de edición"}, "remix": {"model_tip": "Seleccione la versión del modelo de inteligencia artificial para usar en el remix", "image_file": "Imagen de referencia", "image_weight": "Peso de la imagen de referencia", "image_weight_tip": "Ajuste el grado de influencia de la imagen de referencia", "number_images_tip": "Número de resultados de remix generados", "seed_tip": "Controla la aleatoriedad de los resultados del remix", "style_type_tip": "Estilo de la imagen tras el remix, solo aplicable a partir de la versión V_2", "negative_prompt_tip": "Describa los elementos que no desea ver en los resultados del remix", "magic_prompt_option_tip": "Optimización inteligente de las palabras clave para el remix"}, "upscale": {"image_file": "Imagen que se desea ampliar", "resemblance": "<PERSON><PERSON><PERSON><PERSON>", "resemblance_tip": "Controla el nivel de similitud entre el resultado ampliado y la imagen original", "detail": "Detalle", "detail_tip": "Controla el grado de realce de los detalles en la imagen ampliada", "number_images_tip": "Número de resultados de ampliación generados", "seed_tip": "Controla la aleatoriedad del resultado de la ampliación", "magic_prompt_option_tip": "Optimización inteligente de las palabras clave para la ampliación"}, "magic_prompt_option": "Mejora de indicación", "model": "Versión", "aspect_ratio": "Relación de aspecto", "style_type": "<PERSON><PERSON><PERSON>", "learn_more": "Más información", "prompt_placeholder_edit": "Introduce la descripción de tu imagen, utiliza comillas dobles \" \" para texto a dibujar", "proxy_required": "Actualmente es necesario tener un proxy activo para ver las imágenes generadas, en el futuro se soportará conexión directa desde China", "image_file_required": "Por favor, carga una imagen primero", "image_file_retry": "Vuelve a cargar la imagen"}, "plantuml": {"download": {"failed": "Descarga fallida, por favor verifica la conexión a internet", "png": "Descargar PNG", "svg": "Descargar SVG"}, "tabs": {"preview": "Vista previa", "source": "<PERSON><PERSON><PERSON> fuente"}, "title": "Diagrama PlantUML"}, "prompts": {"explanation": "Ayúdame a explicar este concepto", "summarize": "Ayúdame a resumir este párrafo", "title": "Eres un asistente hábil en conversación, debes resumir la conversación del usuario en un título de 10 palabras o menos. El idioma del título debe coincidir con el idioma principal del usuario, no uses signos de puntuación ni otros símbolos especiales"}, "provider": {"aihubmix": "AiHubMix", "burncloud": "BurnCloud", "alayanew": "Alaya NeW", "anthropic": "Antropológico", "azure-openai": "Azure OpenAI", "baichuan": "BaiChuan", "baidu-cloud": "<PERSON><PERSON>", "copilot": "GitHub Copiloto", "dashscope": "Álibaba Nube <PERSON>n", "deepseek": "Profundo Buscar", "dmxapi": "DMXAPI", "doubao": "Volcán Motor", "fireworks": "Fuegos Artificiales", "gemini": "<PERSON><PERSON><PERSON><PERSON>", "gitee-ai": "Gitee AI", "github": "GitHub Modelos", "gpustack": "GPUStack", "grok": "Grok", "groq": "Groq", "hunyuan": "<PERSON><PERSON>", "hyperbolic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infini": "Infini", "jina": "<PERSON><PERSON>", "lmstudio": "Estudio LM", "minimax": "Minimax", "mistral": "<PERSON><PERSON><PERSON>", "modelscope": "ModelScope Módulo", "moonshot": "Lanzamiento Lunar", "nvidia": "Nvidia", "o3": "O3", "ocoolai": "ocoolAI", "ollama": "Ollama", "openai": "OpenAI", "openrouter": "OpenRouter", "perplexity": "Perplejidad", "ppio": "PPIO Cloud Piao", "qwenlm": "QwenLM", "silicon": "<PERSON><PERSON><PERSON>", "stepfun": "Función Salto", "tencent-cloud-ti": "Tencent Nube TI", "together": "Juntos", "xirang": "Telecom Nube XiRang", "yi": "<PERSON><PERSON>o Todo", "zhinao": "360 Inteligente", "zhipu": "ZhiPu IA", "voyageai": "Voyage AI", "qiniu": "<PERSON><PERSON>"}, "restore": {"confirm": "¿Está seguro de que desea restaurar los datos?", "confirm.button": "Seleccionar archivo de respaldo", "content": "La operación de restauración sobrescribirá todos los datos actuales de la aplicación con los datos de respaldo. Tenga en cuenta que el proceso de restauración puede llevar algún tiempo, gracias por su paciencia.", "progress": {"completed": "Restauración completada", "copying_files": "Copiando archivos... {{progress}}%", "extracting": "Descomprimiendo la copia de seguridad...", "preparing": "Preparando la restauración...", "reading_data": "Leyendo datos...", "title": "Progreso de Restauración"}, "title": "Restauración de Datos"}, "settings": {"about": "Acerca de nosotros", "about.checkingUpdate": "Verificando actualizaciones...", "about.checkUpdate": "Comprobar actualizaciones", "about.checkUpdate.available": "<PERSON><PERSON><PERSON><PERSON>ora", "about.contact.button": "Correo electrónico", "about.contact.title": "Contacto por correo electrónico", "about.description": "Una asistente de IA creada para los creadores", "about.downloading": "Descargando actualización...", "about.feedback.button": "Enviar feedback", "about.feedback.title": "Enviar comentarios", "about.license.button": "<PERSON>er", "about.license.title": "Licencia", "about.releases.button": "<PERSON>er", "about.releases.title": "Registro de cambios", "about.social.title": "Cuentas sociales", "about.title": "Acerca de nosotros", "about.updateAvailable": "Versión nueva disponible {{version}}", "about.updateError": "Error de actualización", "about.updateNotAvailable": "Tu software ya está actualizado", "about.website.button": "<PERSON>er", "about.website.title": "Sitio web oficial", "advanced.auto_switch_to_topics": "Cambiar automáticamente a temas", "advanced.title": "Configuración avanzada", "assistant": "<PERSON><PERSON><PERSON> predeterminado", "assistant.model_params": "Parámetros del modelo", "assistant.title": "<PERSON><PERSON><PERSON> predeterminado", "data": {"app_data": "Datos de la aplicación", "app_knowledge": "Archivo de base de conocimientos", "app_knowledge.button.delete": "Eliminar archivo", "app_knowledge.remove_all": "Eliminar archivos de la base de conocimientos", "app_knowledge.remove_all_confirm": "Eliminar los archivos de la base de conocimientos reducirá el uso del espacio de almacenamiento, pero no eliminará los datos vectorizados de la base de conocimientos. Después de la eliminación, no se podrán abrir los archivos originales. ¿Desea eliminarlos?", "app_knowledge.remove_all_success": "Archivos eliminados con éxito", "app_logs": "Registros de la aplicación", "clear_cache": {"button": "Lim<PERSON><PERSON> caché", "confirm": "Limpiar caché eliminará los datos de la caché de la aplicación, incluyendo los datos de las aplicaciones mini. Esta acción no se puede deshacer, ¿desea continuar?", "error": "Error al limpiar la caché", "success": "Caché limpia con éxito", "title": "Lim<PERSON><PERSON> caché"}, "data.title": "Directorio de datos", "hour_interval_one": "{{count}} hora", "hour_interval_other": "{{count}} horas", "joplin": {"check": {"button": "Rev<PERSON><PERSON>", "empty_token": "Por favor, ingrese primero el token de autorización de Joplin", "empty_url": "Por favor, ingrese primero la URL de escucha del servicio de recorte de Joplin", "fail": "La validación de la conexión de Joplin falló", "success": "La validación de la conexión de Joplin fue exitosa"}, "help": "En las opciones de Joplin, habilita el servicio de recorte de páginas web (sin necesidad de instalar una extensión del navegador), confirma el número de puerto y copia el token de autorización", "title": "Configuración de Joplin", "token": "Token de autorización de Joplin", "token_placeholder": "Introduce el token de autorización de Joplin", "url": "URL a la que escucha el servicio de recorte de Joplin", "url_placeholder": "http://127.0.0.1:41184/"}, "markdown_export.force_dollar_math.help": "Al activarlo, al exportar a Markdown se usarán $$ para marcar las fórmulas LaTeX. Nota: Esto también afectará a todas las formas de exportación a través de Markdown, como Notion, Yuque, etc.", "markdown_export.force_dollar_math.title": "Forzar el uso de $$ para marcar fórmulas LaTeX", "markdown_export.help": "Si se especifica, se guardará automáticamente en esta ruta cada vez que se exporte; de lo contrario, se mostrará un cuadro de diálogo para guardar", "markdown_export.path": "Ruta de exportación predeterminada", "markdown_export.path_placeholder": "Ruta de exportación", "markdown_export.select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "markdown_export.title": "Exportar Markdown", "minute_interval_one": "{{count}} minuto", "minute_interval_other": "{{count}} minutos", "notion.api_key": "Clave de API de Notion", "notion.api_key_placeholder": "Introduzca la clave de API de Notion", "notion.auto_split": "Dividir automáticamente las conversaciones al exportar", "notion.auto_split_tip": "Cuando se exportan temas largos, se dividirán automáticamente en páginas en Notion", "notion.check": {"button": "Verificar", "empty_api_key": "API key no configurada", "empty_database_id": "Database ID no configurado", "error": "Conexión anormal, por favor verifica la red y si el API key y Database ID son correctos", "fail": "Conexión fallida, por favor verifica la red y si el API key y Database ID son correctos", "success": "Conexión exitosa"}, "notion.database_id": "ID de la base de datos de Notion", "notion.database_id_placeholder": "Introduzca el ID de la base de datos de Notion", "notion.help": "Documentación de configuración de Notion", "notion.page_name_key": "Campo del nombre de la página", "notion.page_name_key_placeholder": "Introduzca el campo del nombre de la página, por defecto es Nombre", "notion.split_size": "Tamaño de la división automática", "notion.split_size_help": "Para usuarios gratuitos de Notion, se recomienda establecerlo en 90, y para usuarios avanzados, en 24990. El valor predeterminado es 90", "notion.split_size_placeholder": "Introduzca el límite de bloques por página (predeterminado 90)", "notion.title": "Configuración de Notion", "obsidian": {"title": "Configuración de Obsidian", "default_vault": "Repositorio Obsidian predeterminado", "default_vault_placeholder": "Seleccione un repositorio Obsidian predeterminado", "default_vault_loading": "Obteniendo repositorios Obsidian...", "default_vault_no_vaults": "No se encontraron repositorios Obsidian", "default_vault_fetch_error": "Error al obtener los repositorios Obsidian", "default_vault_export_failed": "Exportación fallida"}, "title": "Configuración de datos", "webdav": {"autoSync": "Sincronización automática", "autoSync.off": "Desactivar", "backup.button": "Hacer copia de seguridad en WebDAV", "backup.modal.filename.placeholder": "Ingrese el nombre del archivo de copia de seguridad", "backup.modal.title": "Hacer copia de seguridad en WebDAV", "host": "Dirección WebDAV", "host.placeholder": "http://localhost:8080", "hour_interval_one": "{{count}} hora", "hour_interval_other": "{{count}} horas", "lastSync": "Última copia de seguridad", "minute_interval_one": "{{count}} minuto", "minute_interval_other": "{{count}} minutos", "noSync": "Esperando la próxima copia de seguridad", "password": "Contraseña WebDAV", "path": "Ruta WebDAV", "path.placeholder": "/backup", "restore.button": "Restaurar desde WebDAV", "restore.confirm.content": "La restauración desde WebDAV sobrescribirá los datos actuales, ¿desea continuar?", "restore.confirm.title": "Confirmar restauración", "restore.content": "La restauración desde WebDAV sobrescribirá los datos actuales, ¿desea continuar?", "restore.modal.select.placeholder": "Seleccione el archivo de copia de seguridad a restaurar", "restore.modal.title": "Restaurar desde WebDAV", "restore.title": "Restaurar desde WebDAV", "syncError": "Error de copia de seguridad", "syncStatus": "Estado de copia de seguridad", "title": "WebDAV", "user": "Nombre de usuario WebDAV", "maxBackups": "Número máximo de copias de seguridad", "maxBackups.unlimited": "Sin límite", "backup.manager.title": "Gestión de copias de seguridad", "backup.manager.refresh": "Actualizar", "backup.manager.delete.selected": "Eliminar seleccionados", "backup.manager.delete.text": "Eliminar", "backup.manager.restore.text": "Restaurar", "backup.manager.restore.success": "Restauración exitosa, la aplicación se actualizará en unos segundos", "backup.manager.restore.error": "Fallo en la restauración", "backup.manager.delete.confirm.title": "Confirmar eliminación", "backup.manager.delete.confirm.single": "¿Está seguro de que desea eliminar el archivo de copia de seguridad \"{{fileName}}\"? Esta acción no se puede deshacer.", "backup.manager.delete.confirm.multiple": "¿Está seguro de que desea eliminar los {{count}} archivos de copia de seguridad seleccionados? Esta acción no se puede deshacer.", "backup.manager.delete.success.single": "Eliminación exitosa", "backup.manager.delete.success.multiple": "Se eliminaron exitosamente {{count}} archivos de copia de seguridad", "backup.manager.delete.error": "Fallo al eliminar", "backup.manager.fetch.error": "No se pudo obtener el archivo de copia de seguridad", "backup.manager.select.files.delete": "Seleccione los archivos de copia de seguridad a eliminar", "backup.manager.columns.fileName": "Nombre del archivo", "backup.manager.columns.modifiedTime": "Fecha de modificación", "backup.manager.columns.size": "<PERSON><PERSON><PERSON>", "backup.manager.columns.actions": "Acciones"}, "yuque": {"check": {"button": "Verificar", "empty_repo_url": "Por favor, ingrese primero la URL del repositorio de conocimientos", "empty_token": "Por favor, ingrese primero el Token de YuQue", "fail": "La validación de la conexión de YuQue falló", "success": "La validación de la conexión de YuQue fue exitosa"}, "help": "Obtener el Token de Yuque", "repo_url": "URL del repositorio de conocimiento", "repo_url_placeholder": "https://www.yuque.com/username/xxx", "title": "Configuración de Yuque", "token": "Token de Yuque", "token_placeholder": "Ingrese el Token de Yuque"}, "export_menu": {"title": "Exportar configuración del menú", "image": "Exportar como imagen", "markdown": "Exportar a Markdown", "markdown_reason": "Exportar a Markdown (con pensamiento incluido)", "notion": "Exportar a Notion", "yuque": "Exportar a Yuque", "obsidian": "Exportar a Obsidian", "siyuan": "Exportar a Siyuan Notes", "joplin": "Exportar a Jo<PERSON>lin", "docx": "Exportar a Word"}, "siyuan": {"check": {"title": "Prueba de conexión", "button": "Probar", "empty_config": "Por favor, complete la dirección API y el token", "success": "Conexión exitosa", "fail": "Fallo en la conexión, verifique la dirección API y el token", "error": "Error inesperado, verifique la conexión de red"}, "title": "Configuración de Siyuan Notas", "api_url": "Dirección API", "api_url_placeholder": "Ejemplo: http://127.0.0.1:6806", "token": "Token API", "token.help": "Obtener en Siyuan Notas -> Configuración -> Acerca de", "token_placeholder": "Por favor ingrese el token de <PERSON>", "box_id": "ID del Cuaderno", "box_id_placeholder": "Por favor ingrese el ID del cuaderno", "root_path": "Ruta raíz del documento", "root_path_placeholder": "Ejemplo: /CherryStudio"}, "nutstore": {"title": "Configuración de Nutstore", "isLogin": "Iniciado se<PERSON>", "notLogin": "No iniciado sesión", "login.button": "<PERSON><PERSON><PERSON>", "logout.button": "<PERSON><PERSON><PERSON>", "logout.title": "¿Seguro que quieres cerrar la sesión de Nutstore?", "logout.content": "Después de cerrar sesión no podrás hacer copias de seguridad ni restaurar desde Nutstore", "checkConnection.name": "Verificar conexión", "checkConnection.success": "Conexión con Nutstore establecida", "checkConnection.fail": "Fallo en la conexión con Nutstore", "username": "Nombre de usuario de Nutstore", "path": "Ruta de almacenamiento de Nutstore", "path.placeholder": "Por favor ingrese la ruta de almacenamiento de Nutstore", "backup.button": "Hacer copia de seguridad en Nutstore", "restore.button": "Restaurar desde Nutstore", "pathSelector.title": "Ruta de almacenamiento de Nutstore", "pathSelector.return": "Volver", "pathSelector.currentPath": "<PERSON><PERSON> actual", "new_folder.button.confirm": "Aceptar", "new_folder.button.cancel": "<PERSON><PERSON><PERSON>", "new_folder.button": "<PERSON><PERSON><PERSON> carpeta"}, "divider.basic": "Configuración básica", "divider.cloud_storage": "Configuración de almacenamiento en la nube", "divider.export_settings": "Configuración de exportación", "divider.third_party": "Conexiones de terceros", "message_title.use_topic_naming.title": "Usar el modelo de nombramiento temático para crear títulos de mensajes exportados", "message_title.use_topic_naming.help": "Al activarlo, se utilizará el modelo de nombramiento temático para generar títulos de mensajes exportados. Esta opción también afectará a todos los métodos de exportación mediante Markdown."}, "display.assistant.title": "Configuración del asistente", "display.custom.css": "CSS personalizado", "display.custom.css.cherrycss": "Obtener desde cherrycss.com", "display.custom.css.placeholder": "/* Escribe tu CSS personalizado aquí */", "display.sidebar.chat.hiddenMessage": "El asistente es una función básica y no se puede ocultar", "display.sidebar.disabled": "Iconos ocultos", "display.sidebar.empty": "Arrastra las funciones que deseas ocultar desde la izquierda aquí", "display.sidebar.files.icon": "Mostrar icono de archivos", "display.sidebar.knowledge.icon": "Mostrar icono de conocimiento", "display.sidebar.minapp.icon": "Mostrar icono de miniprogramas", "display.sidebar.painting.icon": "Mostrar icono de pintura", "display.sidebar.title": "Configuración de barra lateral", "display.sidebar.translate.icon": "Mostrar icono de traducción", "display.sidebar.visible": "Iconos visibles", "display.title": "Configuración de visualización", "display.zoom.title": "Configuración de zoom", "display.topic.title": "Configuración de tema", "font_size.title": "Tamaño de fuente de mensajes", "general": "Configuración general", "general.avatar.reset": "Restablecer avatar", "general.backup.button": "Hacer copia de seguridad", "general.backup.title": "Copia de seguridad y restauración de datos", "general.display.title": "Configuración de visualización", "general.emoji_picker": "Selector de emojis", "general.image_upload": "Carga de imágenes", "general.reset.button": "Restablecer", "general.reset.title": "Restablecer datos", "general.restore.button": "Restaurar", "general.title": "Configuración general", "general.user_name": "Nombre de usuario", "general.user_name.placeholder": "Ingresa un nombre de usuario", "general.view_webdav_settings": "Ver configuración WebDAV", "input.auto_translate_with_space": "Traducir con tres espacios rápidos", "input.target_language": "Idioma objetivo", "input.target_language.chinese": "Chino simplificado", "input.target_language.chinese-traditional": "Chino tradicional", "input.target_language.english": "Inglés", "input.target_language.japanese": "Japonés", "input.target_language.russian": "<PERSON><PERSON><PERSON>", "launch.onboot": "Iniciar automáticamente al encender", "launch.title": "<PERSON><PERSON>o", "launch.totray": "Minimizar a la bandeja al iniciar", "mcp": {"actions": "Acciones", "active": "Activar", "addError": "Fallo al agregar servidor", "addServer": "Ag<PERSON><PERSON> servidor", "addSuccess": "<PERSON><PERSON><PERSON> ag<PERSON> exitosamente", "args": "Argumentos", "argsTooltip": "Cada argumento en una línea", "baseUrlTooltip": "Dirección URL remota", "command": "Comand<PERSON>", "config_description": "Configurar modelo de contexto del protocolo del servidor", "deleteError": "Fallo al eliminar servidor", "deleteSuccess": "Servidor eliminado exitosamente", "dependenciesInstall": "Instalar dependencias", "dependenciesInstalling": "Instalando dependencias...", "description": "Descripción", "duplicateName": "Ya existe un servidor con el mismo nombre", "editJson": "<PERSON>ar <PERSON>", "editServer": "<PERSON><PERSON> servid<PERSON>", "env": "Variables de entorno", "envTooltip": "Formato: CLAVE=valor, una por línea", "findMore": "Más servidores MCP", "install": "Instalar", "installError": "Fallo al instalar dependencias", "installSuccess": "Dependencias instaladas exitosamente", "jsonFormatError": "Error de formato JSON", "jsonModeHint": "Edite la representación JSON de la configuración del servidor MCP. Asegúrese de que el formato sea correcto antes de guardar.", "jsonSaveError": "Fallo al guardar la configuración JSON", "jsonSaveSuccess": "Configuración JSON guardada exitosamente", "missingDependencies": "<PERSON>al<PERSON>, instalelas para continuar", "name": "Nombre", "noServers": "No se han configurado servidores", "npx_list": {"actions": "Acciones", "description": "Descripción", "no_packages": "No se encontraron paquetes", "npm": "NPM", "package_name": "Nombre del paquete", "scope_placeholder": "Ingrese el ámbito npm (por ejemplo @your-org)", "scope_required": "Por favor ingrese el ámbito npm", "search": "Buscar", "search_error": "<PERSON><PERSON><PERSON> <PERSON>", "usage": "<PERSON><PERSON>", "version": "Versión"}, "serverPlural": "<PERSON><PERSON><PERSON>", "serverSingular": "<PERSON><PERSON><PERSON>", "title": "Servidores MCP", "type": "Tipo", "updateError": "<PERSON>o al actualizar servidor", "updateSuccess": "Servidor actualizado exitosa<PERSON>e", "url": "URL", "errors": {"32000": "El servidor MCP no se pudo iniciar, verifique si los parámetros están completos según la guía"}, "tabs": {"general": "General", "description": "Descripción", "tools": "Herramientas", "prompts": "Indicaciones", "resources": "Recursos"}, "tools": {"inputSchema": "Esquema de entrada", "availableTools": "Herramientas disponibles", "noToolsAvailable": "No hay herramientas disponibles", "loadError": "Error al cargar las herramientas"}, "prompts": {"availablePrompts": "Indicaciones disponibles", "noPromptsAvailable": "No hay indicaciones disponibles", "arguments": "Argumentos", "requiredField": "Campo obligatorio", "genericError": "Error al obtener la indicación", "loadError": "Fallo al cargar la indicación"}, "resources": {"noResourcesAvailable": "No hay recursos disponibles", "availableResources": "Recursos disponibles", "uri": "URI", "mimeType": "Tipo MIME", "size": "<PERSON><PERSON><PERSON>", "blob": "Datos binarios", "blobInvisible": "Datos binarios ocultos", "text": "Texto"}, "types": {"inMemory": "Integrado", "sse": "SSE", "streamableHttp": "En secuencia", "stdio": "STDIO"}, "sync": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectProvider": "<PERSON><PERSON><PERSON><PERSON><PERSON> proveedor:", "discoverMcpServers": "Detectar servidores MCP", "discoverMcpServersDescription": "Acceder a la plataforma para detectar servidores MCP disponibles", "getToken": "Obtener token de API", "getTokenDescription": "Obtener un token de API personal desde su cuenta", "setToken": "Ingrese su token", "tokenRequired": "Se requiere token de API", "tokenPlaceholder": "Introduzca el token de API aquí", "button": "Sincronizar", "error": "Error al sincronizar el servidor MCP", "success": "Servidor MCP sincronizado correctamente", "unauthorized": "Sincronización no autorizada", "noServersAvailable": "No hay servidores MCP disponibles"}, "sse": "Eventos enviados por el servidor (sse)", "streamableHttp": "HTTP transmisible (streamableHttp)", "stdio": "Entrada/Salida estándar (stdio)", "inMemory": "En memoria", "headers": "Encabezados", "headersTooltip": "Encabezados personalizados para solicitudes HTTP", "searchNpx": "Buscar MCP", "newServer": "Servidor MCP", "startError": "Inicio fall<PERSON>", "editMcpJson": "Editar configuración MCP", "installHelp": "Obtener ayuda de instalación", "deleteServer": "Eliminar servidor", "deleteServerConfirm": "¿Está seguro de que desea eliminar este servidor?", "registry": "Repositorio de paquetes", "registryTooltip": "Seleccione un repositorio para instalar paquetes, útil para resolver problemas de red con el repositorio predeterminado.", "registryDefault": "Predeterminado", "not_support": "El modelo no es compatible", "user": "Usuario", "system": "Sistema", "timeout": "Tiempo de espera", "timeoutTooltip": "Tiempo de espera (en segundos) para las solicitudes a este servidor; el valor predeterminado es 60 segundos", "provider": "<PERSON><PERSON><PERSON><PERSON>", "providerUrl": "URL del proveedor", "logoUrl": "URL del logotipo", "tags": "Etiquetas", "tagsPlaceholder": "Ingrese etiquetas", "providerPlaceholder": "Nombre del proveedor", "advancedSettings": "Configuración avanzada"}, "messages.divider": "Separador de mensajes", "messages.grid_columns": "Número de columnas en la cuadrícula de mensajes", "messages.grid_popover_trigger": "Desencadenante de detalles de cuadrícula", "messages.grid_popover_trigger.click": "Mostrar al hacer clic", "messages.grid_popover_trigger.hover": "Mostrar al pasar el ratón", "messages.input.paste_long_text_as_file": "Pegar texto largo como archivo", "messages.input.paste_long_text_threshold": "Límite de longitud de texto largo", "messages.input.send_shortcuts": "Atajos de teclado para enviar", "messages.input.show_estimated_tokens": "Mostrar número estimado de tokens", "messages.input.title": "Configuración de entrada", "messages.markdown_rendering_input_message": "Renderizar mensajes de entrada en Markdown", "messages.math_engine": "Motor de fórmulas matemáticas", "messages.metrics": "Re<PERSON>o inicial {{time_first_token_millsec}}ms | {{token_speed}} tokens por segundo", "messages.model.title": "Configuración del modelo", "messages.navigation": "Botón de navegación de conversación", "messages.navigation.anchor": "Ancla de conversación", "messages.navigation.buttons": "Botones arriba y abajo", "messages.navigation.none": "No mostrar", "messages.title": "Configuración de mensajes", "messages.use_serif_font": "Usar fuente serif", "model": "<PERSON><PERSON> predeterminado", "models.add.add_model": "Agregar modelo", "models.add.group_name": "Nombre del grupo", "models.add.group_name.placeholder": "<PERSON><PERSON> <PERSON>, ChatGPT", "models.add.group_name.tooltip": "<PERSON><PERSON> <PERSON>, ChatGPT", "models.add.model_id": "ID del modelo", "models.add.model_id.placeholder": "<PERSON>bliga<PERSON><PERSON>, por ejemplo, gpt-3.5-turbo", "models.add.model_id.tooltip": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, gpt-3.5-turbo", "models.add.model_name": "Nombre del modelo", "models.add.model_name.placeholder": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, GPT-3.5", "models.check.all": "Todos", "models.check.all_models_passed": "Todos los modelos pasaron la verificación", "models.check.button_caption": "Verificación de salud", "models.check.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "models.check.enable_concurrent": "Verificación concurrente", "models.check.enabled": "Habilitado", "models.check.failed": "Fallido", "models.check.keys_status_count": "Pasados: {{count_passed}} claves, fallidos: {{count_failed}} claves", "models.check.model_status_summary": "{{provider}}: {{count_passed}} modelos completaron la verificación de salud ({{count_partial}} modelos no accesibles con algunas claves), {{count_failed}} modelos completamente inaccesibles.", "models.check.no_api_keys": "No se encontraron claves API, agrega una clave API primero.", "models.check.passed": "Pasado", "models.check.select_api_key": "Seleccionar clave API a usar:", "models.check.single": "Individual", "models.check.start": "Iniciar", "models.check.title": "Verificación de salud del modelo", "models.check.use_all_keys": "Usar todas las claves", "models.default_assistant_model": "Modelo predeterminado del asistente", "models.default_assistant_model_description": "Modelo utilizado al crear nuevos asistentes, si el asistente no tiene un modelo asignado, se utiliza este modelo", "models.empty": "Sin modelos", "models.enable_topic_naming": "Renombrar temas automáticamente", "models.manage.add_whole_group": "Agregar todo el grupo", "models.manage.remove_whole_group": "Eliminar todo el grupo", "models.topic_naming_model": "Modelo de nombramiento de temas", "models.topic_naming_model_description": "Modelo utilizado para nombrar temas automáticamente", "models.topic_naming_model_setting_title": "Configuración del modelo de nombramiento de temas", "models.topic_naming_prompt": "Sugerencias para nombramiento de temas", "models.translate_model": "<PERSON><PERSON> de traducci<PERSON>", "models.translate_model_description": "Modelo utilizado para el servicio de traducción", "models.translate_model_prompt_message": "Ingrese las sugerencias del modelo de traducción", "models.translate_model_prompt_title": "Sugerencias del modelo de traducción", "moresetting": "Configuración adicional", "moresetting.check.confirm": "Confirma<PERSON>", "moresetting.check.warn": "Ten cuidado al seleccionar esta opción, ¡una elección incorrecta puede causar que los modelos no funcionen correctamente!!!", "moresetting.warn": "Advertencia de riesgo", "provider": {"add.name": "Nombre del proveedor", "add.name.placeholder": "<PERSON><PERSON> <PERSON>, OpenAI", "add.title": "A<PERSON><PERSON><PERSON> proveedor", "add.type": "<PERSON><PERSON><PERSON> de <PERSON>", "api.url.preview": "Vista previa: {{url}}", "api.url.reset": "Restablecer", "api.url.tip": "Ignorar v1 al final con /, forzar uso de dirección de entrada con # al final", "api_host": "Dirección API", "api_key": "Clave API", "api_key.tip": "<PERSON><PERSON><PERSON> m<PERSON><PERSON>les claves con comas", "api_version": "Versión API", "charge": "Recargar", "check": "Verificar", "check_all_keys": "Verificar todas las claves", "check_multiple_keys": "Verificar múltiples claves API", "copilot": {"auth_failed": "Autenticación de Github Copilot fallida", "auth_success": "Autenticación de Github Copilot exitosa", "auth_success_title": "Autenticación exitosa", "code_failed": "Error al obtener Código del Dispositivo, por favor inténtelo de nuevo", "code_generated_desc": "Por favor, copie el Código del Dispositivo en el siguiente enlace del navegador", "code_generated_title": "Obtener Código del Dispositivo", "confirm_login": "El uso excesivo puede llevar al bloqueo de su cuenta de Github, use con precaución!!!!", "confirm_title": "Advertencia de Riesgo", "connect": "Conectar con Github", "custom_headers": "Encabezados personalizados", "description": "Su cuenta de Github necesita suscribirse a Copilot", "expand": "Expandir", "headers_description": "Encabezados personalizados (formato json)", "invalid_json": "Formato JSON incorrecto", "login": "Iniciar se<PERSON><PERSON> en Github", "logout": "<PERSON><PERSON><PERSON> se<PERSON> en Github", "logout_failed": "<PERSON><PERSON>r al cerrar sesión, por favor inténtelo de nuevo", "logout_success": "Ha cerrado sesión exitosamente", "model_setting": "Configuración del modelo", "open_verification_first": "Por favor, haga clic en el enlace superior para acceder a la página de verificación", "rate_limit": "Límite de tasa", "tooltip": "Para usar <PERSON><PERSON><PERSON>pilot, primero debe iniciar sesión en Github"}, "delete.content": "¿Está seguro de que desea eliminar este proveedor de modelos?", "delete.title": "Eliminar proveedor", "docs_check": "<PERSON>er", "docs_more_details": "Obtener más detalles", "get_api_key": "Haga clic aquí para obtener la clave", "is_not_support_array_content": "Activar modo compatible", "not_checked": "No verificado", "remove_duplicate_keys": "Eliminar claves duplicadas", "remove_invalid_keys": "Eliminar claves inválidas", "search": "Buscar plataforma de modelos...", "search_placeholder": "Buscar ID o nombre del modelo", "title": "Ser<PERSON><PERSON> de modelos", "oauth": {"button": "Iniciar se<PERSON><PERSON> con la cuenta de {{provider}}", "description": "Este servicio es proporcionado por <website>{{provider}}</website>", "official_website": "Sitio web oficial"}, "notes": {"title": "Nota del modelo", "placeholder": "Por favor, introduzca el contenido en formato Markdown...", "markdown_editor_default_value": "Área de vista previa"}, "basic_auth": "Autenticación HTTP", "basic_auth.tip": "Aplicable para instancias desplegadas a través del servidor (ver documento). Actualmente solo se admite el esquema Basic (RFC7617).", "basic_auth.user_name": "Nombre de usuario", "basic_auth.user_name.tip": "Déjelo vacío para desactivar", "basic_auth.password": "Contraseña", "bills": "Facturas", "no_models_for_check": "No hay modelos disponibles para revisar (por ejemplo, modelos de conversación)"}, "proxy": {"mode": {"custom": "Proxy personalizado", "none": "No usar proxy", "system": "Proxy del sistema", "title": "Modo de proxy"}, "title": "Configuración de Proxy"}, "proxy.title": "Dirección proxy", "quickAssistant": {"click_tray_to_show": "Haz clic en el icono de la bandeja para iniciar", "enable_quick_assistant": "Habilitar <PERSON>", "read_clipboard_at_startup": "Leer portapapeles al iniciar", "title": "<PERSON><PERSON><PERSON>", "use_shortcut_to_show": "Haz clic derecho en el icono de la bandeja o usa un atajo de teclado para iniciar"}, "shortcuts": {"action": "Acción", "clear_shortcut": "<PERSON><PERSON><PERSON>", "clear_topic": "<PERSON><PERSON><PERSON>", "copy_last_message": "Copiar el último mensaje", "key": "Tecla", "mini_window": "<PERSON><PERSON><PERSON>", "new_topic": "Nuevo tema", "press_shortcut": "Presionar atajo", "reset_defaults": "Restablecer atajos predeterminados", "reset_defaults_confirm": "¿Está seguro de querer restablecer todos los atajos?", "reset_to_default": "Restablecer a predeterminado", "search_message": "Buscar mensaje", "show_app": "Mostrar aplicación", "show_settings": "Abrir configuración", "title": "<PERSON><PERSON><PERSON>", "toggle_new_context": "Limpiar contexto", "toggle_show_assistants": "Alternar visibilidad de asistentes", "toggle_show_topics": "Alternar visibilidad de temas", "zoom_in": "Ampliar interfaz", "zoom_out": "Reducir interfaz", "zoom_reset": "Restablecer zoom"}, "theme.system": "Sistema", "theme.dark": "Oscuro", "theme.light": "<PERSON><PERSON><PERSON>", "theme.title": "<PERSON><PERSON>", "theme.window.style.opaque": "Ventana opaca", "theme.window.style.title": "<PERSON><PERSON><PERSON>", "theme.window.style.transparent": "Ventana transparente", "title": "Configuración", "topic.position": "Posición del tema", "topic.position.left": "Iz<PERSON>erda", "topic.position.right": "Derecha", "topic.show.time": "Mostrar tiempo del tema", "tray.onclose": "Minimizar a la bandeja al cerrar", "tray.show": "Mostrar bandera del sistema", "tray.title": "Bandera", "websearch": {"blacklist": "Lista negra", "blacklist_description": "No aparecerán los resultados de los siguientes sitios web en los resultados de búsqueda", "blacklist_tooltip": "Por favor, use el siguiente formato (separado por saltos de línea)\">\">example.com\">https://www.example.com\">https://example.com\">*://*.example.com", "check": "Comprobar", "check_failed": "Verificación fallida", "check_success": "Verificación exitosa", "get_api_key": "Haz clic aquí para obtener la clave", "no_provider_selected": "Por favor, seleccione un proveedor de búsqueda antes de comprobar", "search_max_result": "Número de resultados de búsqueda", "search_provider": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "search_provider_placeholder": "Seleccione un proveedor de búsqueda", "search_result_default": "Predeterminado", "search_with_time": "Búsqueda con fecha", "tavily": {"api_key": "Clave de API de Tavily", "api_key.placeholder": "Introduce la clave de API de Tavily", "description": "Tavily es un motor de búsqueda diseñado específicamente para agentes de IA, proporcionando resultados en tiempo real, precisos, sugerencias de consulta inteligentes y capacidades de investigación profundas", "title": "<PERSON><PERSON>"}, "title": "Búsqueda en la web", "overwrite": "Sobrescri<PERSON>", "overwrite_tooltip": "Forzar el uso del proveedor de búsqueda en lugar del modelo de lenguaje grande para realizar búsquedas", "subscribe": "Suscripción a lista negra", "subscribe_update": "<PERSON><PERSON><PERSON><PERSON>ora", "subscribe_add": "Agregar suscripción", "subscribe_url": "Dirección del origen de la suscripción", "subscribe_name": "Nombre alternativo", "subscribe_name.placeholder": "Nombre alternativo que se usará cuando el origen de la suscripción descargado no tenga un nombre", "subscribe_add_success": "¡Origen de la suscripción agregado correctamente!", "subscribe_delete": "Eliminar origen de la suscripción", "apikey": "Clave API", "free": "<PERSON><PERSON><PERSON>", "content_limit": "Límite de longitud del contenido", "content_limit_tooltip": "Limita la longitud del contenido de los resultados de búsqueda; el contenido excedente será truncado"}, "miniapps": {"open_link_external": {"title": "<PERSON><PERSON><PERSON> enlace en nueva ventana del navegador"}, "custom": {"title": "Aplicación Pequeña Personalizada", "edit_title": "Editar Aplicación Pequeña Personalizada", "save_success": "La aplicación pequeña personalizada se ha guardado correctamente.", "save_error": "No se pudo guardar la aplicación pequeña personalizada.", "remove_success": "La aplicación pequeña personalizada se eliminó correctamente.", "remove_error": "No se pudo eliminar la aplicación pequeña personalizada.", "logo_upload_success": "El logo se cargó correctamente.", "logo_upload_error": "No se pudo cargar el logo.", "id": "ID", "id_error": "El campo ID es obligatorio.", "id_placeholder": "Por favor, introduzca el ID", "name": "Nombre", "name_error": "El campo Nombre es obligatorio.", "name_placeholder": "Por favor, introduzca el nombre", "url": "URL", "url_error": "El campo URL es obligatorio.", "url_placeholder": "Por favor, introduzca la URL", "logo": "Logo", "logo_url": "URL del Logo", "logo_file": "Cargar Archivo del Logo", "logo_url_label": "URL del Logo", "logo_url_placeholder": "Por favor, introduzca la URL del logo", "logo_upload_label": "<PERSON><PERSON>", "logo_upload_button": "<PERSON><PERSON>", "save": "Guardar", "edit_description": "Edite aquí la configuración de su aplicación pequeña personalizada. Cada aplicación debe incluir los campos id, name, url y logo.", "placeholder": "Introduzca la configuración de la aplicación pequeña personalizada (en formato JSON)", "duplicate_ids": "Se encontraron IDs duplicados: {{ids}}", "conflicting_ids": "Conflictos con IDs de aplicaciones predeterminadas: {{ids}}"}, "title": "Configuración de miniaplicaciones", "disabled": "Miniaplicaciones ocultas", "empty": "Arrastra aquí las miniaplicaciones que deseas ocultar desde la izquierda", "visible": "Miniaplicaciones visibles", "cache_settings": "Configuración de caché", "cache_title": "Cantidad de miniaplicaciones en caché", "cache_description": "Establece el número máximo de miniaplicaciones que pueden permanecer activas simultáneamente", "reset_tooltip": "Restablecer a los valores predeterminados", "display_title": "Configuración de visualización de miniaplicaciones", "sidebar_title": "Visualización de miniaplicaciones activas en la barra lateral", "sidebar_description": "Configura si se muestra o no en la barra lateral la miniaplicación activa", "cache_change_notice": "Los cambios surtirán efecto cuando el número de miniaplicaciones abiertas aumente o disminuya hasta alcanzar el valor configurado"}, "quickPhrase": {"title": "Fr<PERSON> r<PERSON>", "add": "Agregar frase", "edit": "<PERSON>ar frase", "titleLabel": "<PERSON><PERSON><PERSON><PERSON>", "contentLabel": "Contenido", "titlePlaceholder": "Ingrese el título de la frase", "contentPlaceholder": "Ingrese el contenido de la frase. Se admite el uso de variables, y luego puede presionar Tab para ubicar rápidamente la variable y modificarla. Por ejemplo: \\nAyúdame a planificar la ruta desde ${desde} hasta ${hasta}, y luego envíala a ${correo}.", "delete": "Eliminar frase", "deleteConfirm": "Una vez eliminada, la frase no podrá recuperarse. ¿Desea continuar?", "locationLabel": "Agregar ubicación", "global": "Frase global", "assistant": "<PERSON>ase de asistente"}, "quickPanel": {"title": "Menú de acceso rápido", "close": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmar", "back": "Atrás", "forward": "Adelante", "multiple": "Sele<PERSON><PERSON> múl<PERSON>"}, "privacy": {"title": "Configuración de privacidad", "enable_privacy_mode": "Enviar informes de errores y estadísticas de forma anónima"}, "assistant.icon.type": "Tipo de ícono del modelo", "assistant.icon.type.model": "Ícono del modelo", "assistant.icon.type.emoji": "<PERSON><PERSON><PERSON>", "assistant.icon.type.none": "No mostrar", "general.auto_check_update.title": "Actualización automática", "input.show_translate_confirm": "Mostrar diálogo de confirmación de traducción", "messages.prompt": "Palabra de indicación", "messages.input.enable_quick_triggers": "Habilitar menú rápido con '/' y '@'", "messages.input.enable_delete_model": "Habilitar la eliminación con la tecla de borrado para modelos/archivos adjuntos introducidos", "messages.math_engine.none": "<PERSON><PERSON><PERSON>", "models.manage.add_listed": "Agregar modelo de la lista", "models.manage.remove_listed": "Eliminar modelo de la lista", "zoom.title": "Zoom de página"}, "translate": {"any.language": "cualquier idioma", "button.translate": "Traducir", "close": "<PERSON><PERSON><PERSON>", "confirm": {"content": "La traducción reemplazará el texto original, ¿desea continuar?", "title": "Confirmación de traducción"}, "error.failed": "Fallo en la traducción", "error.not_configured": "El modelo de traducción no está configurado", "history": {"clear": "Borrar historial", "clear_description": "Borrar el historial eliminará todos los registros de traducciones, ¿desea continuar?", "delete": "Eliminar", "empty": "Sin historial de traducciones por el momento", "title": "Historial de traducciones"}, "input.placeholder": "Ingrese el texto para traducir", "output.placeholder": "Traducción", "processing": "Traduciendo...", "scroll_sync.disable": "Deshabilitar sincronización de desplazamiento", "scroll_sync.enable": "Habilitar sincronización de desplazamiento", "title": "Traducción", "tooltip.newline": "Salto de línea", "menu": {"description": "Traducir el contenido del campo de entrada actual"}}, "tray": {"quit": "Salir", "show_mini_window": "<PERSON><PERSON><PERSON>", "show_window": "Mostrar ventana"}, "words": {"knowledgeGraph": "Grafo de Conocimiento", "quit": "Salir", "show_window": "<PERSON><PERSON>", "visualization": "Visualización"}, "update": {"title": "Actualización", "message": "Nueva versión {{version}} disponible, ¿desea instalarla ahora?", "later": "<PERSON>ás tarde", "install": "Instalar", "noReleaseNotes": "Sin notas de la versión"}}}