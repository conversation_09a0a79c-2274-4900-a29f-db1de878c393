{"translation": {"agents": {"add.button": "Add to Assistant", "add.knowledge_base": "Knowledge Base", "add.knowledge_base.placeholder": "Select Knowledge Base", "add.name": "Name", "add.name.placeholder": "Enter name", "add.prompt": "Prompt", "add.prompt.placeholder": "Enter prompt", "add.prompt.variables.tip": "Available variables: {{date}}, {{time}}, {{datetime}}, {{system}}, {{arch}}, {{language}}, {{model_name}}", "add.title": "Create Agent", "import": {"title": "Import from External", "type": {"url": "URL", "file": "File"}, "url_placeholder": "Enter JSON URL", "select_file": "Select File", "button": "Import", "file_filter": "JSON Files", "error": {"url_required": "Please enter a URL", "fetch_failed": "Failed to fetch from URL", "invalid_format": "Invalid agent format: missing required fields"}}, "export": {"agent": "Export Agent"}, "delete.popup.content": "Are you sure you want to delete this agent?", "edit.message.add.title": "Add", "edit.message.assistant.placeholder": "Enter assistant message", "edit.message.assistant.title": "Assistant", "edit.message.empty.content": "Conversation input content cannot be empty", "edit.message.group.title": "Message Group", "edit.message.title": "Preset messages", "edit.message.user.placeholder": "Enter user message", "edit.message.user.title": "User", "edit.model.select.title": "Select Model", "edit.settings.hide_preset_messages": "Hide Preset Message", "edit.title": "Edit Agent", "manage.title": "Manage Agents", "my_agents": "My Agents", "search.no_results": "No results found", "sorting.title": "Sorting", "settings": {"title": "Agent Setting"}, "tag.agent": "Agent", "tag.default": "<PERSON><PERSON><PERSON>", "tag.new": "New", "tag.system": "System", "title": "Agents"}, "assistants": {"title": "Assistants", "abbr": "Assistants", "settings.title": "Assistant Settings", "clear.content": "Clearing the topic will delete all topics and files in the assistant. Are you sure you want to continue?", "clear.title": "Clear topics", "copy.title": "Copy Assistant", "delete.content": "Deleting an assistant will delete all topics and files under the assistant. Are you sure you want to delete it?", "delete.title": "Delete Assistant", "edit.title": "Edit Assistant", "save.success": "Saved successfully", "save.title": "Save to agent", "icon.type": "Assistant <PERSON><PERSON>", "search": "Search assistants...", "settings.default_model": "Default Model", "settings.knowledge_base": "Knowledge Base Settings", "settings.mcp": "MCP Servers", "settings.mcp.enableFirst": "Enable this server in MCP settings first", "settings.mcp.title": "MCP Settings", "settings.mcp.noServersAvailable": "No MCP servers available. Add servers in settings", "settings.mcp.description": "Default enabled MCP servers", "settings.model": "Model Settings", "settings.preset_messages": "Preset Messages", "settings.prompt": "Prompt Settings", "settings.reasoning_effort": "Reasoning effort", "settings.reasoning_effort.off": "Off", "settings.reasoning_effort.high": "Think harder", "settings.reasoning_effort.low": "Think less", "settings.reasoning_effort.medium": "Think normally", "settings.reasoning_effort.default": "<PERSON><PERSON><PERSON>", "settings.more": "Assistant Settings", "settings.knowledge_base.recognition.tip": "The assistant will use the large model's intent recognition capability to determine whether to use the knowledge base for answering. This feature will depend on the model's capabilities", "settings.knowledge_base.recognition": "Use Knowledge Base", "settings.knowledge_base.recognition.off": "Force Search", "settings.knowledge_base.recognition.on": "Intent Recognition", "settings.tool_use_mode": "Tool Use Mode", "settings.tool_use_mode.function": "Function", "settings.tool_use_mode.prompt": "Prompt", "settings.regular_phrases": {"title": "Regular Phrase", "add": "Add Phrase", "edit": "Edit Phrase", "delete": "Delete Phrase", "deleteConfirm": "Are you sure to delete this phrase?", "titleLabel": "Title", "titlePlaceholder": "Enter title", "contentLabel": "Content", "contentPlaceholder": "Please enter phrase content, support using variables, and press Tab to quickly locate the variable to modify. For example: \nHelp me plan a route from ${from} to ${to}, and send it to ${email}."}, "list": {"showByList": "List View", "showByTags": "Tag View"}, "tags": {"untagged": "Untagged", "none": "No tags", "manage": "Tag Management", "modify": "Modify Tag", "add": "Add Tag", "delete": "Delete Tag", "deleteConfirm": "Are you sure to delete this tag?", "settings": {"title": "Tag Settings"}}}, "auth": {"error": "API key automatically obtained failed, please get it manually", "get_key": "Get", "get_key_success": "API key automatically obtained successfully", "login": "<PERSON><PERSON>", "oauth_button": "Auth with {{provider}}"}, "backup": {"confirm": "Are you sure you want to backup data?", "confirm.button": "Select Backup Location", "content": "Backup all data, including chat history, settings, and knowledge base. Please note that the backup process may take some time, thank you for your patience.", "progress": {"completed": "Backup completed", "compressing": "Compressing files...", "copying_files": "Copying files... {{progress}}%", "preparing": "Preparing backup...", "title": "Backup Progress", "writing_data": "Writing data..."}, "title": "Data Backup"}, "button": {"add": "Add", "added": "Added", "collapse": "Collapse", "manage": "Manage", "select_model": "Select Model", "show.all": "Show All", "update_available": "Update Available", "includes_user_questions": "Include Your Questions", "case_sensitive": "Case Sensitive", "whole_word": "Whole Word"}, "chat": {"add.assistant.title": "Add Assistant", "artifacts.button.download": "Download", "artifacts.button.openExternal": "Open in external browser", "artifacts.button.preview": "Preview", "artifacts.preview.openExternal.error.content": "Error opening the external browser.", "assistant.search.placeholder": "Search", "deeply_thought": "Deeply thought ({{seconds}} seconds)", "default.description": "Hello, I'm <PERSON><PERSON><PERSON> Assistant. You can start chatting with me right away", "default.name": "Default Assistant", "default.topic.name": "<PERSON><PERSON><PERSON>", "history": {"assistant_node": "Assistant", "click_to_navigate": "Click to navigate to the message", "coming_soon": "Chat workflow diagram coming soon", "no_messages": "No Messages Found", "start_conversation": "Start a conversation to see the chat flow diagram", "title": "Chat History", "user_node": "User", "view_full_content": "View Full Content"}, "input.auto_resize": "Auto resize height", "input.clear": "Clear {{Command}}", "input.clear.content": "Do you want to clear all messages of the current topic?", "input.clear.title": "Clear all messages?", "input.collapse": "Collapse", "input.context_count.tip": "Context / Max Context", "input.estimated_tokens.tip": "Estimated tokens", "input.expand": "Expand", "input.file_not_supported": "Model does not support this file type", "input.file_error": "Error processing file", "input.generate_image": "Generate image", "input.generate_image_not_supported": "The model does not support generating images.", "input.knowledge_base": "Knowledge Base", "input.new.context": "Clear Context {{Command}}", "input.new_topic": "New Topic {{Command}}", "input.pause": "Pause", "input.placeholder": "Type your message here...", "input.send": "Send", "input.settings": "Settings", "input.topics": " Topics ", "input.translate": "Translate to {{target_language}}", "input.upload": "Upload image or document file", "input.upload.document": "Upload document file (model does not support images)", "input.web_search": "Web search", "input.web_search.settings": "Web Search Settings", "input.web_search.button.ok": "Go to Settings", "input.web_search.enable": "Enable web search", "input.web_search.enable_content": "Need to check web search connectivity in settings first", "message.new.branch": "New Branch", "message.new.branch.created": "New Branch Created", "message.new.context": "New Context", "message.quote": "Quote", "message.regenerate.model": "Switch Model", "message.useful": "Helpful", "multiple.select": "Multiple Select", "multiple.select.empty": "No Messages Selected", "navigation": {"first": "Already at the first message", "history": "Chat History", "last": "Already at the last message", "next": "Next Message", "prev": "Previous Message", "top": "Back to top", "bottom": "Back to bottom", "close": "Close"}, "resend": "Resend", "save": "Save", "settings.code.title": "Code Block Settings", "settings.code_editor": {"title": "Code Editor", "highlight_active_line": "Highlight active line", "fold_gutter": "Fold gutter", "autocompletion": "Autocompletion", "keymap": "Keymap"}, "settings.code_execution": {"title": "Code Execution", "tip": "The run button will be displayed in the toolbar of executable code blocks, please do not execute dangerous code!", "timeout_minutes": "Timeout", "timeout_minutes.tip": "The timeout time (minutes) of code execution"}, "settings.code_collapsible": "Code block collapsible", "settings.code_wrappable": "Code block wrappable", "settings.code_cacheable": "Code block cache", "settings.code_cacheable.tip": "Caching code blocks can reduce the rendering time of long code blocks, but it will increase memory usage", "settings.code_cache_max_size": "Max cache size", "settings.code_cache_max_size.tip": "The maximum number of characters allowed to be cached (thousand characters), calculated according to the highlighted code. The length of the highlighted code is much longer than the pure text.", "settings.code_cache_ttl": "<PERSON><PERSON>", "settings.code_cache_ttl.tip": "Cache expiration time (minutes)", "settings.code_cache_threshold": "Cache threshold", "settings.code_cache_threshold.tip": "The minimum number of characters allowed to be cached (thousand characters), calculated according to the actual code. Only code blocks exceeding the threshold will be cached.", "settings.context_count": "Context", "settings.context_count.tip": "The number of previous messages to keep in the context.", "settings.max": "Max", "settings.max_tokens": "Set max tokens", "settings.max_tokens.confirm": "Set max tokens", "settings.max_tokens.confirm_content": "Set the maximum number of tokens the model can generate. Need to consider the context limit of the model, otherwise an error will be reported", "settings.max_tokens.tip": "The maximum number of tokens the model can generate. Need to consider the context limit of the model, otherwise an error will be reported", "settings.reset": "Reset", "settings.set_as_default": "Apply to default assistant", "settings.show_line_numbers": "Show line numbers in code", "settings.temperature": "Temperature", "settings.temperature.tip": "Higher values make the model more creative and unpredictable, while lower values make it more deterministic and precise.", "settings.thought_auto_collapse": "Collapse Thought Content", "settings.thought_auto_collapse.tip": "Automatically collapse thought content after thinking ends", "settings.top_p": "Top-P", "settings.top_p.tip": "Default value is 1, the smaller the value, the less variety in the answers, the easier to understand, the larger the value, the larger the range of the AI's vocabulary, the more diverse", "suggestions.title": "Suggested Questions", "thinking": "Thinking ({{seconds}} seconds)", "topics.auto_rename": "Auto Rename", "topics.clear.title": "Clear Messages", "topics.copy.image": "Copy as image", "topics.copy.md": "<PERSON><PERSON> as markdown", "topics.copy.title": "Copy", "topics.delete.shortcut": "Hold {{key}} to delete directly", "topics.edit.placeholder": "Enter new name", "topics.edit.title": "Edit Name", "topics.export.image": "Export as image", "topics.export.joplin": "Export to <PERSON><PERSON><PERSON>", "topics.export.md": "Export as markdown", "topics.export.md.reason": "Export as Mark<PERSON> (with reasoning)", "topics.export.notion": "Export to Notion", "topics.export.obsidian": "Export to Obsidian", "topics.export.obsidian_vault": "<PERSON><PERSON>", "topics.export.obsidian_vault_placeholder": "Please select the vault name", "topics.export.obsidian_path": "Path", "topics.export.obsidian_path_placeholder": "Please select the path", "topics.export.obsidian_atributes": "Configure Note Attributes", "topics.export.obsidian_btn": "Confirm", "topics.export.obsidian_created": "Creation Time", "topics.export.obsidian_created_placeholder": "Please select the creation time", "topics.export.obsidian_export_failed": "Export failed", "topics.export.obsidian_export_success": "Export success", "topics.export.obsidian_operate": "Operation Method", "topics.export.obsidian_operate_append": "Append", "topics.export.obsidian_operate_new_or_overwrite": "Create New (Overwrite if it exists)", "topics.export.obsidian_operate_placeholder": "Please select the operation method", "topics.export.obsidian_operate_prepend": "Prepend", "topics.export.obsidian_source": "Source", "topics.export.obsidian_source_placeholder": "Please enter the source", "topics.export.obsidian_tags": "Tags", "topics.export.obsidian_tags_placeholder": "Please enter tags, separate multiple tags with commas", "topics.export.obsidian_title": "Title", "topics.export.obsidian_title_placeholder": "Please enter the title", "topics.export.obsidian_title_required": "The title cannot be empty", "topics.export.obsidian_no_vaults": "No Obsidian vaults found", "topics.export.obsidian_loading": "Loading...", "topics.export.obsidian_fetch_error": "Failed to fetch Obsidian vaults", "topics.export.obsidian_fetch_folders_error": "Failed to fetch folder structure", "topics.export.obsidian_no_vault_selected": "Please select a vault first", "topics.export.obsidian_select_vault_first": "Please select a vault first", "topics.export.obsidian_root_directory": "Root Directory", "topics.export.title": "Export", "topics.export.word": "Export as Word", "topics.export.yuque": "Export to Yuque", "topics.list": "Topic List", "topics.move_to": "Move to", "topics.new": "New Topic", "topics.pinned": "Pinned Topics", "topics.prompt": "Topic Prompts", "topics.prompt.edit.title": "Edit Topic Prompts", "topics.prompt.tips": "Topic Prompts: Additional supplementary prompts provided for the current topic", "topics.title": "Topics", "topics.unpinned": "Unpinned Topics", "translate": "Translate", "topics.export.siyuan": "Export to Siyuan Note", "topics.export.wait_for_title_naming": "Generating title...", "topics.export.title_naming_success": "Title generated successfully", "topics.export.title_naming_failed": "Failed to generate title, using default title", "input.translating": "Translating...", "input.upload.upload_from_local": "Upload local file...", "input.web_search.builtin": "Model Built-in", "input.web_search.builtin.enabled_content": "Use the built-in web search function of the model", "input.web_search.builtin.disabled_content": "The current model does not support web search", "input.web_search.no_web_search": "Disable Web Search", "input.web_search.no_web_search.description": "Do not enable web search", "input.tools.collapse": "Collapse", "input.tools.expand": "Expand", "input.tools.collapse_in": "Collapse", "input.tools.collapse_out": "Remove from collapse", "input.thinking": "Thinking", "input.thinking.mode.default": "<PERSON><PERSON><PERSON>", "input.thinking.mode.default.tip": "The model will automatically determine the number of tokens to think", "input.thinking.mode.custom": "Custom", "input.thinking.mode.custom.tip": "The maximum number of tokens the model can think. Need to consider the context limit of the model, otherwise an error will be reported", "input.thinking.mode.tokens.tip": "Set the number of thinking tokens to use.", "input.thinking.budget_exceeds_max": "Thinking budget exceeds the maximum token number"}, "code_block": {"collapse": "Collapse", "copy.failed": "Co<PERSON> failed", "copy.source": "Copy Source Code", "copy.success": "<PERSON>pied", "copy": "Copy", "download.failed.network": "Download failed, please check the network", "download.png": "Download PNG", "download.source": "Download Source Code", "download.svg": "Download SVG", "download": "Download", "edit.save.failed.message_not_found": "Save failed, message not found", "edit.save.failed": "Save failed", "edit.save.success": "Saved", "edit.save": "Save Changes", "edit": "Edit", "expand": "Expand", "more": "More", "preview.copy.image": "Copy as image", "preview.source": "View Source Code", "preview.zoom_in": "Zoom In", "preview.zoom_out": "Zoom Out", "preview": "Preview", "run": "Run", "split.restore": "Restore Split View", "split": "Split View", "wrap.off": "Unwrap", "wrap.on": "Wrap"}, "common": {"add": "Add", "advanced_settings": "Advanced Settings", "and": "and", "assistant": "Assistant", "avatar": "Avatar", "back": "Back", "cancel": "Cancel", "chat": "Cha<PERSON>", "clear": "Clear", "close": "Close", "confirm": "Confirm", "copied": "<PERSON>pied", "copy": "Copy", "inspect": "Inspect", "cut": "Cut", "default": "<PERSON><PERSON><PERSON>", "delete": "Delete", "description": "Description", "docs": "Docs", "download": "Download", "duplicate": "Duplicate", "edit": "Edit", "expand": "Expand", "collapse": "Collapse", "footnote": "Reference content", "footnotes": "References", "fullscreen": "Entered fullscreen mode. Press F11 to exit", "knowledge_base": "Knowledge Base", "language": "Language", "loading": "Loading...", "model": "Model", "models": "Models", "more": "More", "name": "Name", "paste": "Paste", "prompt": "Prompt", "provider": "Provider", "regenerate": "Regenerate", "rename": "<PERSON><PERSON>", "reset": "Reset", "save": "Save", "search": "Search", "select": "Select", "selectedMessages": "Selected {{count}} messages", "success": "Success", "topics": "Topics", "warning": "Warning", "you": "You", "reasoning_content": "Deep reasoning", "sort": {"pinyin": "Sort by <PERSON><PERSON><PERSON>", "pinyin.asc": "Sort by <PERSON><PERSON><PERSON> (A-Z)", "pinyin.desc": "Sort by <PERSON><PERSON><PERSON> (Z-A)"}, "no_results": "No results"}, "docs": {"title": "Docs"}, "error": {"backup.file_format": "Backup file format error", "chat.response": "Something went wrong. Please check if you have set your API key in the Settings > Providers", "http": {"400": "Request failed. Please check if the request parameters are correct. If you have changed the model settings, please reset them to the default settings", "401": "Authentication failed. Please check if your API key is correct", "403": "Access denied. Please check if your account is verified, or contact the service provider for more information", "404": "Model not found or request path is incorrect", "429": "Too many requests. Please try again later", "500": "Server error. Please try again later", "502": "Gateway error. Please try again later", "503": "Service unavailable. Please try again later", "504": "Gateway timeout. Please try again later"}, "model.exists": "Model already exists", "no_api_key": "API key is not configured", "provider_disabled": "Model provider is not enabled", "render": {"description": "Failed to render message content. Please check if the message content format is correct", "title": "Render Error"}, "user_message_not_found": "Cannot find original user message to resend", "unknown": "Unknown error", "pause_placeholder": "Paused"}, "export": {"assistant": "Assistant", "attached_files": "Attached Files", "conversation_details": "Conversation Details", "conversation_history": "Conversation History", "created": "Created", "last_updated": "Last Updated", "messages": "Messages", "user": "User"}, "files": {"actions": "Actions", "all": "All Files", "count": "files", "created_at": "Created At", "delete": "Delete", "delete.content": "Deleting a file will delete its reference from all messages. Are you sure you want to delete this file?", "delete.paintings.warning": "Image contains this file, deletion is not possible", "delete.title": "Delete File", "document": "Document", "edit": "Edit", "file": "File", "image": "Image", "name": "Name", "open": "Open", "size": "Size", "text": "Text", "title": "Files", "type": "Type"}, "gpustack": {"keep_alive_time.description": "The time in minutes to keep the connection alive, default is 5 minutes.", "keep_alive_time.placeholder": "Minutes", "keep_alive_time.title": "Keep Alive Time", "title": "GPUStack"}, "history": {"continue_chat": "Continue <PERSON>", "locate.message": "Locate the message", "search.messages": "Search All Messages", "search.placeholder": "Search topics or messages...", "search.topics.empty": "No topics found, press Enter to search all messages", "title": "Topics Search"}, "knowledge": {"add": {"title": "Add Knowledge Base"}, "add_directory": "Add Directory", "add_file": "Add File", "add_note": "Add Note", "add_sitemap": "Website Map", "add_url": "Add URL", "cancel_index": "Cancel Indexing", "chunk_overlap": "<PERSON><PERSON>", "chunk_overlap_placeholder": "Default (not recommended to change)", "chunk_overlap_tooltip": "The amount of duplicate content between adjacent chunks, ensuring that the chunks are still contextually related, improving the overall effect of processing long text", "chunk_size": "Chunk Size", "chunk_size_change_warning": "Chunk size and overlap size changes only apply to new content", "chunk_size_placeholder": "Default (not recommended to change)", "chunk_size_too_large": "Chunk size cannot exceed model context limit ({{max_context}})", "chunk_size_tooltip": "Split documents into chunks, each chunk size, not exceeding model context limit", "clear_selection": "Clear selection", "delete": "Delete", "delete_confirm": "Are you sure you want to delete this knowledge base?", "directories": "Directories", "directory_placeholder": "Enter Directory Path", "document_count": "Requested Document Chunks", "document_count_default": "<PERSON><PERSON><PERSON>", "document_count_help": "The more document chunks requested, the more information is included, but the more tokens are consumed", "drag_file": "Drag file here", "edit_remark": "Edit Remark", "edit_remark_placeholder": "Please enter remark content", "empty": "No knowledge base found", "file_hint": "Support {{file_types}}", "index_all": "Index All", "index_cancelled": "Indexing cancelled", "index_started": "Indexing started", "invalid_url": "Invalid URL", "model_info": "Model Info", "no_bases": "No knowledge bases available", "no_match": "No matching content found in the knowledge base.", "no_provider": "Knowledge base model provider is not set, the knowledge base will no longer be supported, please create a new knowledge base", "not_set": "Not Set", "not_support": "Knowledge base database engine updated, the knowledge base will no longer be supported, please create a new knowledge base", "notes": "Notes", "notes_placeholder": "Enter additional information or context for this knowledge base...", "rename": "<PERSON><PERSON>", "search": "Search knowledge base", "search_placeholder": "Enter text to search", "settings": "Knowledge Base Settings", "sitemap_placeholder": "Enter Website Map URL", "sitemaps": "Websites", "source": "Source", "status": "Status", "status_completed": "Completed", "status_failed": "Failed", "status_new": "Added", "status_pending": "Pending", "status_processing": "Processing", "threshold": "Matching threshold", "threshold_placeholder": "Not set", "threshold_too_large_or_small": "Threshold cannot be greater than 1 or less than 0", "threshold_tooltip": "Used to evaluate the relevance between the user's question and the content in the knowledge base (0-1)", "title": "Knowledge Base", "topN": "Number of results returned", "topN_too_large_or_small": "The number of results returned cannot be greater than 30 or less than 1.", "topN_placeholder": "Not set", "topN_tooltip": "The number of matching results returned; the larger the value, the more matching results, but also the more tokens consumed.", "url_added": "URL added", "url_placeholder": "Enter URL, multiple URLs separated by Enter", "urls": "URLs", "dimensions": "Embedding dimension", "dimensions_size_tooltip": "The size of the embedding dimension; the larger the value, the larger the embedding dimension, but it also consumes more tokens.", "dimensions_size_placeholder": "Default value (modification not recommended)", "dimensions_size_too_large": "The embedding dimension cannot exceed the model's context limit ({{max_context}})."}, "languages": {"arabic": "Arabic", "chinese": "Chinese", "chinese-traditional": "Traditional Chinese", "english": "English", "french": "French", "german": "German", "italian": "Italian", "japanese": "Japanese", "korean": "Korean", "portuguese": "Portuguese", "russian": "Russian", "spanish": "Spanish", "polish": "Polish", "turkish": "Turkish", "thai": "Thai", "vietnamese": "Vietnamese", "indonesian": "Indonesian", "urdu": "Urdu", "malay": "Malay"}, "lmstudio": {"keep_alive_time.description": "The time in minutes to keep the connection alive, default is 5 minutes.", "keep_alive_time.placeholder": "Minutes", "keep_alive_time.title": "Keep Alive Time", "title": "LM Studio"}, "message": {"agents": {"imported": "Imported successfully", "import.error": "Import failed"}, "api.check.model.title": "Select the model to use for detection", "api.connection.failed": "Connection failed", "api.connection.success": "Connection successful", "assistant.added.content": "Assistant added successfully", "attachments": {"pasted_image": "Pasted Image", "pasted_text": "Pasted Text"}, "backup.failed": "Backup failed", "backup.start.success": "Backup started", "backup.success": "Backup successful", "chat.completion.paused": "Chat completion paused", "citation": "{{count}} citations", "citations": "References", "copied": "Copied!", "copy.failed": "Co<PERSON> failed", "copy.success": "Copied!", "delete.confirm.title": "Delete Confirmation", "delete.confirm.content": "Are you sure you want to delete the selected {{count}} message(s)?", "delete.failed": "Delete Failed", "delete.success": "Delete Successful", "empty_url": "Failed to download image, possibly due to prompt containing sensitive content or prohibited words", "error.chunk_overlap_too_large": "Chunk overlap cannot be greater than chunk size", "error.dimension_too_large": "Content size is too large", "error.enter.api.host": "Please enter your API host first", "error.enter.api.key": "Please enter your API key first", "error.enter.model": "Please select a model first", "error.enter.name": "Please enter the name of the knowledge base", "error.fetchTopicName": "Failed to name the topic", "error.get_embedding_dimensions": "Failed to get embedding dimensions", "error.invalid.api.host": "Invalid API Host", "error.invalid.api.key": "Invalid API Key", "error.invalid.enter.model": "Please select a model", "error.invalid.proxy.url": "Invalid proxy URL", "error.invalid.webdav": "Invalid WebDAV settings", "error.joplin.export": "Failed to export to Joplin. Please keep Joplin running and check connection status or configuration", "error.joplin.no_config": "Joplin Authorization Token or URL is not configured", "error.invalid.nutstore": "Invalid Nutstore settings", "error.invalid.nutstore_token": "Invalid Nutstore Token", "error.markdown.export.preconf": "Failed to export the Markdown file to the preconfigured path", "error.markdown.export.specified": "Failed to export the Markdown file", "error.notion.export": "Failed to export to Notion. Please check connection status and configuration according to documentation", "error.notion.no_api_key": "Notion ApiKey or Notion DatabaseID is not configured", "error.yuque.export": "Failed to export to Yuque. Please check connection status and configuration according to documentation", "error.yuque.no_config": "<PERSON><PERSON> or <PERSON><PERSON> is not configured", "group.delete.content": "Deleting a group message will delete the user's question and all assistant's answers", "group.delete.title": "Delete Group Message", "ignore.knowledge.base": "Web search mode is enabled, ignore knowledge base", "info.notion.block_reach_limit": "Dialogue too long, exporting to Notion in pages", "loading.notion.exporting_progress": "Exporting to Notion ({{current}}/{{total}})...", "loading.notion.preparing": "Preparing to export to Notion...", "mention.title": "Switch model answer", "message.code_style": "Code style", "message.delete.content": "Are you sure you want to delete this message?", "message.delete.title": "Delete Message", "message.multi_model_style": "Group style", "message.multi_model_style.fold": "Fold view", "message.multi_model_style.fold.compress": "Switch to compact layout", "message.multi_model_style.fold.expand": "Switch to expanded layout", "message.multi_model_style.grid": "Grid layout", "message.multi_model_style.horizontal": "Side by side", "message.multi_model_style.vertical": "Stacked view", "message.style": "Message style", "message.style.bubble": "Bubble", "message.style.plain": "Plain", "processing": "Processing...", "regenerate.confirm": "Regenerating will replace current message", "reset.confirm.content": "Are you sure you want to clear all data?", "reset.double.confirm.content": "All data will be lost, do you want to continue?", "reset.double.confirm.title": "DATA LOST !!!", "restore.failed": "Rest<PERSON> failed", "restore.success": "Restored successfully", "save.success.title": "Saved successfully", "searching": "Searching...", "success.joplin.export": "Successfully exported to Joplin", "success.markdown.export.preconf": "Successfully exported the Markdown file to the preconfigured path", "success.markdown.export.specified": "Successfully exported the Markdown file", "success.notion.export": "Successfully exported to Notion", "success.yuque.export": "Successfully exported to Yuque", "switch.disabled": "Please wait for the current reply to complete", "tools": {"completed": "Completed", "invoking": "Invoking", "error": "Error occurred", "raw": "Raw", "preview": "Preview"}, "topic.added": "New topic added", "upgrade.success.button": "<PERSON><PERSON>", "upgrade.success.content": "Please restart the application to complete the upgrade", "upgrade.success.title": "Upgrade successfully", "warn.notion.exporting": "Exporting to Notion, please do not request export repeatedly!", "warning.rate.limit": "Too many requests. Please wait {{seconds}} seconds before trying again.", "error.siyuan.export": "Failed to export to Siyuan Note, please check connection status and configuration according to documentation", "error.siyuan.no_config": "Siyuan Note API address or token is not configured", "success.siyuan.export": "Successfully exported to Siyuan Note", "warn.yuque.exporting": "Exporting to Yuque, please do not request export repeatedly!", "warn.siyuan.exporting": "Exporting to Siyuan Note, please do not request export repeatedly!", "download.success": "Download successfully", "download.failed": "Download failed"}, "minapp": {"popup": {"refresh": "Refresh", "goBack": "Go Back", "goForward": "Go Forward", "close": "Close MinApp", "minimize": "Minimize MinApp", "devtools": "Developer Tools", "openExternal": "Open in Browser", "rightclick_copyurl": "Right-click to copy URL", "open_link_external_on": "Current: Open links in browser", "open_link_external_off": "Current: Open links in default window"}, "sidebar": {"add": {"title": "Add to Sidebar"}, "remove": {"title": "Remove from Sidebar"}, "remove_custom": {"title": "Delete Custom App"}, "hide": {"title": "<PERSON>de"}, "close": {"title": "Close"}, "closeall": {"title": "Close All"}}, "title": "MinApp"}, "miniwindow": {"clipboard": {"empty": "Clipboard is empty"}, "feature": {"chat": "Answer this question", "explanation": "Explanation", "summary": "Content summary", "translate": "Text translation"}, "footer": {"copy_last_message": "Press C to copy", "backspace_clear": "Backspace to clear", "esc": "ESC to {{action}}", "esc_back": "return", "esc_close": "close"}, "input": {"placeholder": {"empty": "Ask {{model}} for help...", "title": "What do you want to do with this text?"}}, "tooltip": {"pin": "Keep Window on Top"}}, "models": {"add_parameter": "Add Parameter", "all": "All", "custom_parameters": "Custom Parameters", "dimensions": "Dimensions {{dimensions}}", "edit": "Edit Model", "embedding": "Embedding", "embedding_model": "Embedding Model", "embedding_model_tooltip": "Add in Settings->Model Provider->Manage", "function_calling": "Function Calling", "no_matches": "No models available", "parameter_name": "Parameter Name", "parameter_type": {"boolean": "Boolean", "json": "JSON", "number": "Number", "string": "Text"}, "pinned": "Pinned", "rerank_model": "Reordering Model", "rerank_model_support_provider": "Currently, the reordering model only supports some providers ({{provider}})", "rerank_model_not_support_provider": "Currently, the reordering model does not support this provider ({{provider}})", "rerank_model_tooltip": "Click the Manage button in Settings -> Model Services to add.", "search": "Search models...", "stream_output": "Stream output", "enable_tool_use": "Enable Tool Use", "type": {"embedding": "Embedding", "free": "Free", "function_calling": "Tool", "reasoning": "Reasoning", "rerank": "Reordering", "select": "Select Model Types", "text": "Text", "vision": "Vision", "websearch": "WebSearch"}}, "navbar": {"expand": "Expand Dialog", "hide_sidebar": "<PERSON><PERSON>bar", "show_sidebar": "Show Sidebar"}, "notification": {"assistant": "Assistant Response", "knowledge.success": "Successfully added {{type}} to the knowledge base", "knowledge.error": "Failed to add {{type}} to knowledge base: {{error}}"}, "ollama": {"keep_alive_time.description": "The time in minutes to keep the connection alive, default is 5 minutes.", "keep_alive_time.placeholder": "Minutes", "keep_alive_time.title": "Keep Alive Time", "title": "Ollama"}, "paintings": {"button.delete.image": "Delete Image", "button.delete.image.confirm": "Are you sure you want to delete this image?", "button.new.image": "New Image", "guidance_scale": "Guidance Scale", "guidance_scale_tip": "Classifier Free Guidance. How close you want the model to stick to your prompt when looking for a related image to show you", "image.size": "Image Size", "inference_steps": "Inference Steps", "inference_steps_tip": "The number of inference steps to perform. More steps produce higher quality but take longer", "negative_prompt": "Negative Prompt", "negative_prompt_tip": "Describe what you don't want included in the image", "number_images": "Number Images", "number_images_tip": "Number of images to generate (1-4)", "prompt_enhancement": "Prompt Enhancement", "prompt_enhancement_tip": "Rewrite prompts into detailed, model-friendly versions when switched on", "prompt_placeholder": "Describe the image you want to create, e.g. A serene lake at sunset with mountains in the background", "regenerate.confirm": "This will replace your existing generated images. Do you want to continue?", "seed": "Seed", "seed_tip": "The same seed and prompt can produce similar images", "seed_desc_tip": "The same seed and prompt can generate similar images, setting -1 will generate different results each time", "title": "Images", "magic_prompt_option": "Magic Prompt", "model": "Model", "aspect_ratio": "Aspect Ratio", "style_type": "Style", "rendering_speed": "Rendering Speed", "learn_more": "Learn More", "paint_course": "tutorial", "prompt_placeholder_edit": "Enter your image description, text drawing uses \"double quotes\" to wrap", "prompt_placeholder_en": "Enter your image description, currently Imagen only supports English prompts", "proxy_required": "Open the proxy and enable “TUN mode” to view generated images or copy them to the browser for opening. In the future, domestic direct connection will be supported", "image_file_required": "Please upload an image first", "image_file_retry": "Please re-upload an image first", "image_placeholder": "No image available", "image_retry": "Retry", "translating": "Translating...", "style_types": {"auto": "Auto", "general": "General", "realistic": "Realistic", "design": "Design", "3d": "3D", "anime": "Anime"}, "rendering_speeds": {"default": "<PERSON><PERSON><PERSON>", "turbo": "Turbo", "quality": "Quality"}, "quality_options": {"auto": "Auto", "low": "Low", "medium": "Medium", "high": "High"}, "moderation_options": {"auto": "Auto", "low": "Low"}, "background_options": {"auto": "Auto", "transparent": "Transparent", "opaque": "Opaque"}, "aspect_ratios": {"square": "Square", "portrait": "Portrait", "landscape": "Landscape"}, "person_generation_options": {"allow_all": "Allow all", "allow_adult": "Allow adult", "allow_none": "Not allowed"}, "quality": "Quality", "moderation": "Moderation", "background": "Background", "mode": {"generate": "Draw", "edit": "Edit", "remix": "Remix", "upscale": "Upscale"}, "generate": {"model_tip": "Model version: V3 is the latest version, V2 is the previous model, V2A is the fast model, V_1 is the first-generation model, _TURBO is the acceleration version", "number_images_tip": "Number of images to generate", "seed_tip": "Controls image generation randomness for reproducible results", "negative_prompt_tip": "Describe unwanted elements, only for V_1, V_1_TURBO, V_2, and V_2_TURBO", "magic_prompt_option_tip": "Intelligently enhances prompts for better results", "style_type_tip": "Image generation style for V_2 and above", "rendering_speed_tip": "Controls rendering speed vs. quality trade-off, only available for V_3", "person_generation": "Generate person", "person_generation_tip": "Allow model to generate person images"}, "edit": {"image_file": "Edited Image", "model_tip": "V3 and V2 versions supported", "number_images_tip": "Number of edited results to generate", "style_type_tip": "Style for edited image, only for V_2 and above", "seed_tip": "Controls editing randomness", "magic_prompt_option_tip": "Intelligently enhances editing prompts", "rendering_speed_tip": "Controls rendering speed vs. quality trade-off, only available for V_3"}, "remix": {"model_tip": "Select AI model version for remixing", "image_file": "Reference Image", "image_weight": "Reference Image Weight", "image_weight_tip": "Adjust reference image influence", "number_images_tip": "Number of remix results to generate", "seed_tip": "Control the randomness of the mixed result", "style_type_tip": "Style for remixed image, only for V_2 and above", "negative_prompt_tip": "Describe unwanted elements in remix results", "magic_prompt_option_tip": "Intelligently enhances remix prompts", "rendering_speed_tip": "Controls rendering speed vs. quality trade-off, only available for V_3"}, "upscale": {"image_file": "Image to upscale", "resemblance": "Similarity", "resemblance_tip": "Controls similarity to original image", "detail": "Detail", "detail_tip": "Controls detail enhancement level", "number_images_tip": "Number of upscaled results to generate", "seed_tip": "Controls upscaling randomness", "magic_prompt_option_tip": "Intelligently enhances upscaling prompts"}, "text_desc_required": "Please enter image description first", "req_error_text": "Operation failed. Please try again. Avoid using 'copyrighted' or 'sensitive' words in your prompt.", "auto_create_paint": "Auto-create image", "auto_create_paint_tip": "After the image is generated, a new image will be created automatically."}, "prompts": {"explanation": "Explain this concept to me", "summarize": "Summarize this text", "title": "You are an assistant who is good at conversation. You need to summarize the user's conversation into a title of 10 characters or less, ensuring it matches the user's primary language without using punctuation or other special symbols."}, "provider": {"aihubmix": "AiHubMix", "burncloud": "BurnCloud", "alayanew": "Alaya NeW", "anthropic": "Anthropic", "azure-openai": "Azure OpenAI", "baichuan": "Baichuan", "baidu-cloud": "Baidu Cloud", "copilot": "GitHub Copilot", "dashscope": "Alibaba Cloud", "deepseek": "DeepSeek", "dmxapi": "DMXAPI", "doubao": "Volcengine", "fireworks": "Fireworks", "gemini": "Gemini", "gitee-ai": "Gitee AI", "github": "GitHub Models", "gpustack": "GPUStack", "grok": "Grok", "groq": "Groq", "hunyuan": "<PERSON>cent <PERSON>", "hyperbolic": "Hyperbolic", "infini": "Infini", "jina": "<PERSON><PERSON>", "lmstudio": "LM Studio", "minimax": "MiniMax", "mistral": "<PERSON><PERSON><PERSON>", "modelscope": "ModelScope", "moonshot": "Moonshot", "nvidia": "Nvidia", "o3": "O3", "ocoolai": "ocoolAI", "ollama": "Ollama", "openai": "OpenAI", "openrouter": "OpenRouter", "perplexity": "Perplexity", "ppio": "PPIO", "qwenlm": "QwenLM", "silicon": "SiliconFlow", "stepfun": "<PERSON><PERSON><PERSON>", "tencent-cloud-ti": "Tencent Cloud TI", "together": "Together", "xirang": "State Cloud Xirang", "yi": "<PERSON>", "zhinao": "360AI", "zhipu": "ZHIPU AI", "voyageai": "Voyage AI", "qiniu": "<PERSON><PERSON>", "tokenflux": "TokenFlux"}, "restore": {"confirm": "Are you sure you want to restore data?", "confirm.button": "Select Backup File", "content": "Restore operation will overwrite all current application data with the backup data. Please note that the restore process may take some time, thank you for your patience.", "progress": {"completed": "Restore completed", "copying_files": "Copying files... {{progress}}%", "extracting": "Extracting backup...", "preparing": "Preparing restore...", "reading_data": "Reading data...", "title": "Restore Progress"}, "title": "Data Restore"}, "settings": {"about": "About & Feedback", "about.checkingUpdate": "Checking for updates...", "about.checkUpdate": "Check Update", "about.checkUpdate.available": "Update", "about.contact.button": "Email", "about.contact.title": "Contact", "about.debug.title": "Debug", "about.debug.open": "Open", "about.description": "A powerful AI assistant for producer", "about.downloading": "Downloading...", "about.feedback.button": "<PERSON><PERSON><PERSON>", "about.feedback.title": "<PERSON><PERSON><PERSON>", "about.license.button": "License", "about.license.title": "License", "about.releases.button": "Releases", "about.releases.title": "Release Notes", "about.social.title": "Social Accounts", "about.title": "About", "about.updateAvailable": "Found new version {{version}}", "about.updateError": "Update error", "about.updateNotAvailable": "You are using the latest version", "about.website.button": "Website", "about.website.title": "Official Website", "advanced.auto_switch_to_topics": "Auto switch to topic", "advanced.title": "Advanced Settings", "assistant": "Default Assistant", "assistant.model_params": "Model Parameters", "assistant.icon.type": "Model Icon Type", "assistant.icon.type.model": "Model Icon", "assistant.icon.type.emoji": "Em<PERSON>ji <PERSON>", "assistant.icon.type.none": "<PERSON>de", "assistant.title": "Default Assistant", "data": {"app_data": "App Data", "app_knowledge": "Knowledge Base Files", "app_knowledge.button.delete": "Delete File", "app_knowledge.remove_all": "Remove Knowledge Base Files", "app_knowledge.remove_all_confirm": "Deleting knowledge base files will reduce the storage space occupied, but will not delete the knowledge base vector data, after deletion, the source file will no longer be able to be opened. Continue?", "app_knowledge.remove_all_success": "Files removed successfully", "app_logs": "App Logs", "backup.skip_file_data_title": "<PERSON>", "backup.skip_file_data_help": "<PERSON>p backing up data files such as pictures and knowledge bases during backup, and only back up chat records and settings. Reduce space occupancy and speed up the backup speed.", "clear_cache": {"button": "<PERSON>ache", "confirm": "Clearing the cache will delete application cache data, including minapp data. This action is irreversible, continue?", "error": "Error clearing cache", "success": "<PERSON><PERSON> cleared", "title": "<PERSON>ache"}, "data.title": "Data Directory", "divider.basic": "Basic Data Settings", "divider.cloud_storage": "Cloud Backup Settings", "divider.export_settings": "Export Settings", "divider.third_party": "Third-party Connections", "hour_interval_one": "{{count}} hour", "hour_interval_other": "{{count}} hours", "export_menu": {"title": "Export Menu <PERSON>s", "image": "Export as Image", "markdown": "Export as <PERSON><PERSON>", "markdown_reason": "Export as Mark<PERSON> (with reasoning)", "notion": "Export to Notion", "yuque": "Export to Yuque", "obsidian": "Export to Obsidian", "siyuan": "Export to SiYuan Note", "joplin": "Export to <PERSON><PERSON><PERSON>", "docx": "Export as Word"}, "joplin": {"check": {"button": "Check", "empty_token": "Please enter Joplin Authorization Token", "empty_url": "Please enter Joplin Clipper Service URL", "fail": "Joplin connection verification failed", "success": "Joplin connection verification successful"}, "help": "In <PERSON><PERSON><PERSON> options, enable the web clipper (no browser extension needed), confirm the port, and copy the auth token here.", "title": "<PERSON><PERSON><PERSON> Configuration", "token": "Joplin Authorization Token", "token_placeholder": "Joplin Authorization Token", "url": "Joplin Web Clipper Service URL", "url_placeholder": "http://127.0.0.1:41184/"}, "markdown_export.force_dollar_math.help": "When enabled, $$ will be forcibly used to mark LaTeX formulas when exporting to Markdown. Note: This option also affects all export methods through Markdown, such as Notion, Yuque, etc.", "markdown_export.force_dollar_math.title": "Force $$ for LaTeX formulas", "markdown_export.help": "If provided, exports will be automatically saved to this path; otherwise, a save dialog will appear.", "markdown_export.path": "Default Export Path", "markdown_export.path_placeholder": "Export Path", "markdown_export.select": "Select", "markdown_export.title": "Markdown Export", "minute_interval_one": "{{count}} minute", "minute_interval_other": "{{count}} minutes", "notion.api_key": "Notion API Key", "notion.api_key_placeholder": "Enter Notion API Key", "notion.auto_split": "Auto split when exporting", "notion.auto_split_tip": "Automatically split pages when exporting long topics to Notion", "notion.check": {"button": "Check", "empty_api_key": "API key is not configured", "empty_database_id": "Database ID is not configured", "error": "Connection error, please check network configuration and API key and Database ID", "fail": "Connection failed, please check network and API key and Database ID", "success": "Connection successful"}, "notion.database_id": "Notion Database ID", "notion.database_id_placeholder": "Enter Notion Database ID", "notion.help": "Notion Configuration Documentation", "notion.page_name_key": "Page Title Field Name", "notion.page_name_key_placeholder": "Enter page title field name, default is Name", "notion.split_size": "Split size", "notion.split_size_help": "Recommended: 90 for Free plan, 24990 for Plus plan, default is 90", "notion.split_size_placeholder": "Enter block limit per page (default 90)", "notion.title": "Notion Configuration", "title": "Data Settings", "webdav": {"autoSync": "Auto Backup", "autoSync.off": "Off", "backup.button": "Backup to WebDAV", "backup.modal.filename.placeholder": "Please enter backup filename", "backup.modal.title": "Backup to WebDAV", "backup.manager.title": "Backup Data Management", "backup.manager.refresh": "Refresh", "backup.manager.delete.selected": "Delete Selected", "backup.manager.delete.text": "Delete", "backup.manager.restore.text": "Rest<PERSON>", "backup.manager.restore.success": "Restore successful, application will refresh shortly", "backup.manager.restore.error": "Rest<PERSON> failed", "backup.manager.delete.confirm.title": "Confirm Delete", "backup.manager.delete.confirm.single": "Are you sure you want to delete backup file \"{{fileName}}\"? This action cannot be undone.", "backup.manager.delete.confirm.multiple": "Are you sure you want to delete {{count}} selected backup files? This action cannot be undone.", "backup.manager.delete.success.single": "Deleted successfully", "backup.manager.delete.success.multiple": "Successfully deleted {{count}} backup files", "backup.manager.delete.error": "Delete failed", "backup.manager.fetch.error": "Failed to get backup files", "backup.manager.select.files.delete": "Please select backup files to delete", "backup.manager.columns.fileName": "Filename", "backup.manager.columns.modifiedTime": "Modified Time", "backup.manager.columns.size": "Size", "backup.manager.columns.actions": "Actions", "host": "WebDAV Host", "host.placeholder": "http://localhost:8080", "hour_interval_one": "{{count}} hour", "hour_interval_other": "{{count}} hours", "lastSync": "Last Backup", "minute_interval_one": "{{count}} minute", "minute_interval_other": "{{count}} minutes", "noSync": "Waiting for next backup", "password": "WebDAV Password", "path": "WebDAV Path", "path.placeholder": "/backup", "restore.button": "Restore from WebDAV", "restore.confirm.content": "Restoring from WebDAV will overwrite current data. Do you want to continue?", "restore.confirm.title": "Confirm <PERSON>ore", "restore.content": "Restore from WebDAV will overwrite the current data, continue?", "restore.modal.select.placeholder": "Please select a backup file to restore", "restore.modal.title": "Restore from WebDAV", "restore.title": "Restore from WebDAV", "syncError": "Backup Error", "syncStatus": "Backup Status", "title": "WebDAV", "user": "WebDAV User", "maxBackups": "Maximum Backups", "maxBackups.unlimited": "Unlimited"}, "yuque": {"check": {"button": "Check", "empty_repo_url": "Please enter the knowledge base URL first", "empty_token": "Please enter the Yuque Token first", "fail": "Yuque connection verification failed", "success": "Yuque connection verified successfully"}, "help": "Get <PERSON>", "repo_url": "Yuque URL", "repo_url_placeholder": "https://www.yuque.com/username/xxx", "title": "Yuque Configuration", "token": "<PERSON><PERSON>", "token_placeholder": "Please enter the Yuque Token"}, "obsidian": {"title": "Obsidian Configuration", "default_vault": "Default Obsidian Vault", "default_vault_placeholder": "Please select the default Obsidian vault", "default_vault_loading": "Loading Obsidian vault...", "default_vault_no_vaults": "No Obsidian vaults found", "default_vault_fetch_error": "Failed to fetch Obsidian vault", "default_vault_export_failed": "Export failed"}, "siyuan": {"title": "Siyuan Note Configuration", "api_url": "Siyuan Note API URL", "api_url_placeholder": "e.g.: http://127.0.0.1:6806", "token": "Siyuan Note Token", "token.help": "Get Siyuan Note Token", "token_placeholder": "Please enter Siyuan Note Token", "box_id": "Siyuan Note Box ID", "box_id_placeholder": "Please enter Siyuan Note Box ID", "root_path": "Siyuan Note Root Path", "root_path_placeholder": "e.g.: /CherryStudio", "check": {"title": "Connection Check", "button": "Check", "empty_config": "Please fill in the API address and token", "success": "Connection successful", "fail": "Connection failed, please check API address and token", "error": "Connection error, please check network connection"}}, "nutstore": {"title": "Nutstore Configuration", "isLogin": "Logged in", "notLogin": "Not logged in", "login.button": "<PERSON><PERSON>", "logout.button": "Logout", "logout.title": "Are you sure you want to logout from Nutstore?", "logout.content": "After logout, you will not be able to backup to Nutstore or restore from Nutstore.", "checkConnection.name": "Check Connection", "checkConnection.success": "Connected to Nutstore", "checkConnection.fail": "Nutstore connection failed", "username": "Nutstore Username", "path": "Nutstore Storage Path", "path.placeholder": "Enter Nutstore storage path", "backup.button": "Backup to Nutstore", "restore.button": "<PERSON><PERSON> from Nutstore", "pathSelector.title": "Nutstore Storage Path", "pathSelector.return": "Return", "pathSelector.currentPath": "Current Path", "new_folder.button.confirm": "Confirm", "new_folder.button.cancel": "Cancel", "new_folder.button": "New Folder"}, "message_title.use_topic_naming.title": "Use topic naming model to create titles for exported messages", "message_title.use_topic_naming.help": "When enabled, use topic naming model to create titles for exported messages. This will also affect all Markdown export methods."}, "display.assistant.title": "Assistant Settings", "display.custom.css": "Custom CSS", "display.custom.css.cherrycss": "Get from cherrycss.com", "display.custom.css.placeholder": "/* Put custom CSS here */", "display.sidebar.chat.hiddenMessage": "Assistants are basic functions, not supported for hiding", "display.sidebar.disabled": "Hide icons", "display.sidebar.empty": "Drag the hidden feature from the left side here", "display.sidebar.files.icon": "Show Files icon", "display.sidebar.knowledge.icon": "Show Knowledge icon", "display.sidebar.minapp.icon": "Show MinApp icon", "display.sidebar.painting.icon": "Show Painting icon", "display.sidebar.title": "Sidebar Settings", "display.sidebar.translate.icon": "Show Translate icon", "display.sidebar.visible": "Show icons", "display.title": "Display Settings", "display.zoom.title": "<PERSON>m <PERSON>s", "display.topic.title": "Topic Settings", "miniapps": {"title": "Mini Apps Settings", "custom": {"title": "Custom", "edit_title": "Edit Custom Mini App", "save_success": "Custom mini app saved successfully.", "save_error": "Failed to save custom mini app.", "remove_success": "Custom mini app removed successfully.", "remove_error": "Failed to remove custom mini app.", "logo_upload_success": "Logo uploaded successfully.", "logo_upload_error": "Failed to upload logo.", "id": "ID", "id_error": "ID is required.", "id_placeholder": "Enter ID", "name": "Name", "name_error": "Name is required.", "name_placeholder": "Enter name", "url": "URL", "url_error": "URL is required.", "url_placeholder": "Enter URL", "logo": "Logo", "logo_url": "Logo URL", "logo_file": "Upload Logo File", "logo_url_label": "Logo URL", "logo_url_placeholder": "Enter logo URL", "logo_upload_label": "Upload Logo", "logo_upload_button": "Upload", "save": "Save", "edit_description": "Edit custom mini app configuration here. Each app should include id, name, url, and logo fields.", "placeholder": "Enter custom mini app configuration (JSON format)", "duplicate_ids": "Duplicate IDs found: {{ids}}", "conflicting_ids": "Conflicting IDs with default apps: {{ids}}"}, "disabled": "Hidden Mini Apps", "empty": "Drag mini apps from the left to hide them", "visible": "Visible Mini Apps", "open_link_external": {"title": "Open new-window links in browser"}, "cache_settings": "<PERSON><PERSON>", "cache_title": "Mini App Cache Limit", "cache_description": "Set the maximum number of active mini apps to keep in memory", "reset_tooltip": "Reset to default", "display_title": "Mini App Display Settings", "sidebar_title": "Sidebar Active Mini Apps Display", "sidebar_description": "Show active mini apps in the sidebar", "cache_change_notice": "Changes will take effect when the number of open mini apps reaches the set value"}, "font_size.title": "Message font size", "general": "General Settings", "general.avatar.reset": "Reset Avatar", "general.backup.button": "Backup", "general.backup.title": "Data Backup and Recovery", "general.display.title": "Display Settings", "general.emoji_picker": "<PERSON><PERSON><PERSON>", "general.image_upload": "Image Upload", "general.auto_check_update.title": "Auto Update", "general.reset.button": "Reset", "general.reset.title": "Data Reset", "general.restore.button": "Rest<PERSON>", "general.title": "General Settings", "general.user_name": "User Name", "general.user_name.placeholder": "Enter your name", "general.view_webdav_settings": "View WebDAV settings", "input.auto_translate_with_space": "Quickly translate with 3 spaces", "input.show_translate_confirm": "Show translation confirmation dialog", "input.target_language": "Target language", "input.target_language.chinese": "Simplified Chinese", "input.target_language.chinese-traditional": "Traditional Chinese", "input.target_language.english": "English", "input.target_language.japanese": "Japanese", "input.target_language.russian": "Russian", "launch.onboot": "Start Automatically on Boot", "launch.title": "Launch", "launch.totray": "Minimize to Tray on Launch", "mcp": {"actions": "Actions", "active": "Active", "addError": "Failed to add server", "addServer": "Add Server", "addServer.create": "Quick Create", "addServer.importFrom": "Import from JSON", "addServer.importFrom.tooltip": "Please copy the configuration JSON (prioritizing\n NPX or UVX configurations) from the MCP Servers introduction page and paste it into the input box.", "addServer.importFrom.placeholder": "Paste MCP server JSON config", "addServer.importFrom.invalid": "Invalid input, please check JSON format", "addServer.importFrom.nameExists": "Server already exists: {{name}}", "addServer.importFrom.oneServer": "Only one MCP server configuration at a time", "addServer.importFrom.connectionFailed": "Connection failed", "addSuccess": "Server added successfully", "args": "Arguments", "argsTooltip": "Each argument on a new line", "baseUrlTooltip": "Remote server base URL", "command": "Command", "sse": "Server-Sent Events (sse)", "streamableHttp": "Streamable HTTP (streamableHttp)", "stdio": "Standard Input/Output (stdio)", "inMemory": "Memory", "config_description": "Configure Model Context Protocol servers", "disable": "Disable MCP Server", "disable.description": "Do not enable MCP server functionality", "deleteError": "Failed to delete server", "deleteSuccess": "Server deleted successfully", "dependenciesInstall": "Install Dependencies", "dependenciesInstalling": "Installing dependencies...", "description": "Description", "noDescriptionAvailable": "No description available", "duplicateName": "A server with this name already exists", "editJson": "Edit JSON", "editServer": "Edit Server", "env": "Environment Variables", "envTooltip": "Format: KEY=value, one per line", "headers": "Headers", "headersTooltip": "Custom headers for HTTP requests", "findMore": "Find More MCP", "searchNpx": "Search MCP", "install": "Install", "installError": "Failed to install dependencies", "installSuccess": "Dependencies installed successfully", "jsonFormatError": "JSON formatting error", "jsonModeHint": "Edit the JSON representation of the MCP server configuration. Please ensure the format is correct before saving.", "jsonSaveError": "Failed to save JSON configuration.", "jsonSaveSuccess": "JSON configuration has been saved.", "missingDependencies": "is Missing, please install it to continue.", "name": "Name", "noServers": "No servers configured", "newServer": "MCP Server", "npx_list": {"actions": "Actions", "description": "Description", "no_packages": "No packages found", "npm": "NPM", "package_name": "Package Name", "scope_placeholder": "Enter npm scope (e.g. @your-org)", "scope_required": "Please enter npm scope", "search": "Search", "search_error": "Search error", "usage": "Usage", "version": "Version"}, "errors": {"32000": "MCP server failed to start, please check the parameters according to the tutorial"}, "serverPlural": "servers", "serverSingular": "server", "title": "MCP Servers", "startError": "Start failed", "type": "Type", "updateError": "Failed to update server", "updateSuccess": "Server updated successfully", "url": "URL", "editMcpJson": "Edit MCP Configuration", "installHelp": "Get Installation Help", "tabs": {"general": "General", "description": "Description", "tools": "Tools", "prompts": "Prompts", "resources": "Resources"}, "tools": {"inputSchema": "Input Schema", "availableTools": "Available Tools", "noToolsAvailable": "No tools available", "loadError": "Get tools Error"}, "prompts": {"availablePrompts": "Available Prompts", "noPromptsAvailable": "No prompts available", "arguments": "Arguments", "requiredField": "Required Field", "genericError": "Get prompt Error", "loadError": "Get prompts Error"}, "resources": {"noResourcesAvailable": "No resources available", "availableResources": "Available Resources", "uri": "URI", "mimeType": "MIME Type", "size": "Size", "blob": "Blob", "blobInvisible": "Blob Invisible", "text": "Text"}, "deleteServer": "Delete Server", "deleteServerConfirm": "Are you sure you want to delete this server?", "registry": "Package Registry", "registryTooltip": "Choose the registry for package installation to resolve network issues with the default registry.", "registryDefault": "<PERSON><PERSON><PERSON>", "not_support": "Model not supported", "user": "User", "system": "System", "types": {"inMemory": "In Memory", "sse": "SSE", "streamableHttp": "Streamable HTTP", "stdio": "STDIO"}, "sync": {"title": "Sync Servers", "selectProvider": "Select Provider:", "discoverMcpServers": "Discover MCP Servers", "discoverMcpServersDescription": "Visit the platform to discover available MCP servers", "getToken": "Get API Token", "getTokenDescription": "Retrieve your personal API token from your account", "setToken": "Enter Your Token", "tokenRequired": "API Token is required", "tokenPlaceholder": "Enter API token here", "button": "Sync", "error": "Sync MCP Servers error", "success": "Sync MCP Servers successful", "unauthorized": "Sync Unauthorized", "noServersAvailable": "No MCP servers available"}, "timeout": "Timeout", "timeoutTooltip": "Timeout in seconds for requests to this server, default is 60 seconds", "provider": "Provider", "providerUrl": "Provider URL", "logoUrl": "Logo URL", "tags": "Tags", "tagsPlaceholder": "Enter tags", "providerPlaceholder": "Provider name", "advancedSettings": "Advanced Settings"}, "messages.prompt": "Show prompt", "messages.tokens": "Show token usage", "messages.divider": "Show divider between messages", "messages.grid_columns": "Message grid display columns", "messages.grid_popover_trigger": "Grid detail trigger", "messages.grid_popover_trigger.click": "Click to display", "messages.grid_popover_trigger.hover": "Hover to display", "messages.input.paste_long_text_as_file": "Paste long text as file", "messages.input.paste_long_text_threshold": "Paste long text length", "messages.input.send_shortcuts": "Send shortcuts", "messages.input.show_estimated_tokens": "Show estimated tokens", "messages.input.title": "Input Settings", "messages.input.enable_quick_triggers": "Enable / and @ triggers", "messages.input.enable_delete_model": "Enable the backspace key to delete models/attachments.", "messages.markdown_rendering_input_message": "Markdown render input message", "messages.math_engine": "Math engine", "messages.math_engine.none": "None", "messages.metrics": "{{time_first_token_millsec}}ms to first token | {{token_speed}} tok/sec", "messages.model.title": "Model Settings", "messages.navigation": "Navigation bar", "messages.navigation.anchor": "Message Anchor", "messages.navigation.buttons": "Navigation Buttons", "messages.navigation.none": "None", "messages.title": "Message Settings", "messages.use_serif_font": "Use serif font", "model": "Default Model", "models.add.add_model": "Add Model", "models.add.group_name": "Group Name", "models.add.group_name.placeholder": "Optional e.g. ChatGPT", "models.add.group_name.tooltip": "Optional e.g. ChatGPT", "models.add.model_id": "Model ID", "models.add.model_id.placeholder": "Required e.g. gpt-3.5-turbo", "models.add.model_id.select.placeholder": "Select Model", "models.add.model_id.tooltip": "Example: gpt-3.5-turbo", "models.add.model_name": "Model Name", "models.add.model_name.placeholder": "Optional e.g. GPT-4", "models.check.all": "All", "models.check.all_models_passed": "All models check passed", "models.check.button_caption": "Health check", "models.check.disabled": "Disabled", "models.check.enable_concurrent": "Concurrent", "models.check.enabled": "Enabled", "models.check.failed": "Failed", "models.check.keys_status_count": "Passed: {{count_passed}} keys, failed: {{count_failed}} keys", "models.check.model_status_failed": "{{count}} models completely inaccessible", "models.check.model_status_partial": "{{count}} models had inaccessible keys", "models.check.model_status_passed": "{{count}} models passed health checks", "models.check.model_status_summary": "{{provider}}: {{summary}}", "models.check.no_api_keys": "No API keys found, please add API keys first.", "models.check.passed": "Passed", "models.check.select_api_key": "Select the API key to use:", "models.check.single": "Single", "models.check.start": "Start", "models.check.title": "Model health check", "models.check.use_all_keys": "Key(s)", "models.check.disclaimer": "Health check requires sending requests, please use it with caution. Models that charge per request may incur additional costs, please bear the responsibility.", "models.default_assistant_model": "De<PERSON>ult Assistant Model", "models.default_assistant_model_description": "Model used when creating a new assistant, if the assistant is not set, this model will be used", "models.empty": "No models found", "models.enable_topic_naming": "Topic Auto Naming", "models.manage.add_listed": "Add models to the list", "models.manage.remove_listed": "Remove models from the list", "models.manage.add_whole_group": "Add the whole group", "models.manage.remove_whole_group": "Remove the whole group", "models.topic_naming_model": "Topic Naming Model", "models.topic_naming_model_description": "Model used when automatically naming a new topic", "models.topic_naming_model_setting_title": "Topic Naming Model Settings", "models.topic_naming_prompt": "Topic Naming Prompt", "models.translate_model": "Translate Model", "models.translate_model_description": "Model used for translation service", "models.translate_model_prompt_message": "Please enter the translate model prompt", "models.translate_model_prompt_title": "Translate Model Prompt", "models.quick_assistant_model": "Quick Assistant Model", "models.quick_assistant_model_description": "Default model used by Quick Assistant", "moresetting": "More Settings", "moresetting.check.confirm": "Confirm Selection", "moresetting.check.warn": "Please be cautious when selecting this option. Incorrect selection may cause the model to malfunction!", "moresetting.warn": "Risk Warning", "notification": {"title": "Notification Settings", "assistant": "Assistant Message", "backup": "Backup Message", "knowledge_embed": "KnowledgeBase Message"}, "provider": {"add.name": "Provider Name", "add.name.placeholder": "Example: OpenAI", "add.title": "Add Provider", "add.type": "Provider Type", "api.url.preview": "Preview: {{url}}", "api.url.reset": "Reset", "api.url.tip": "Ending with / ignores v1, ending with # forces use of input address", "api_host": "API Host", "api_key": "API Key", "api_key.tip": "Multiple keys separated by commas", "api_version": "API Version", "basic_auth": "HTTP authentication", "basic_auth.tip": "Applicable to instances deployed remotely (see the documentation). Currently, only the Basic scheme (RFC 7617) is supported.", "basic_auth.user_name": "Username", "basic_auth.user_name.tip": "Left empty to disable", "basic_auth.password": "Password", "basic_auth.password.tip": "", "charge": "Balance Recharge", "bills": "Fee Bills", "check": "Check", "check_all_keys": "Check All Keys", "check_multiple_keys": "Check Multiple API Keys", "oauth": {"button": "Login with {{provider}}", "description": "This service is provided by <website>{{provider}}</website>", "official_website": "Official Website"}, "openai": {"alert": "OpenAI Provider no longer support the old calling methods. If using a third-party API, please create a new service provider."}, "copilot": {"auth_failed": "Github Copilot authentication failed.", "auth_success": "GitHub Copilot authentication successful.", "auth_success_title": "Certification successful.", "code_failed": "Failed to obtain Device Code, please try again.", "code_generated_desc": "Please copy the device code into the browser link below.", "code_generated_title": "Obtain Device Code", "confirm_login": "Excessive use may lead to your Github account being banned, please use it cautiously!!!!", "confirm_title": "Risk Warning", "connect": "Connect to Github", "custom_headers": "Custom request header", "description": "Your GitHub account needs to subscribe to Copilot.", "expand": "Expand", "headers_description": "Custom request headers (JSON format)", "invalid_json": "JSON format error", "login": "Log in to Github", "logout": "Exit GitHub", "logout_failed": "Exit failed, please try again.", "logout_success": "Successfully logged out.", "model_setting": "Model settings", "open_verification_first": "Please click the link above to access the verification page.", "rate_limit": "Rate limiting", "tooltip": "You need to log in to Github before using Github Copilot"}, "dmxapi": {"select_platform": "Select the platform"}, "delete.content": "Are you sure you want to delete this provider?", "delete.title": "Delete Provider", "docs_check": "Check", "docs_more_details": "for more details", "get_api_key": "Get API Key", "is_not_support_array_content": "Enable compatible mode", "no_models_for_check": "No models available for checking (e.g. chat models)", "not_checked": "Not Checked", "remove_duplicate_keys": "Remove <PERSON>", "remove_invalid_keys": "Remove Invalid Keys", "search": "Search Providers...", "search_placeholder": "Search model id or name", "title": "Model Provider", "notes": {"title": "Model Notes", "placeholder": "Enter Markdown content...", "markdown_editor_default_value": "Preview area"}}, "proxy": {"mode": {"custom": "Custom Proxy", "none": "No Proxy", "system": "System Proxy", "title": "Proxy Mode"}, "title": "Proxy Settings"}, "proxy.title": "Proxy Address", "quickAssistant": {"click_tray_to_show": "Click the tray icon to start", "enable_quick_assistant": "Enable Quick Assistant", "read_clipboard_at_startup": "Read clipboard at startup", "title": "Quick Assistant", "use_shortcut_to_show": "Right-click the tray icon or use shortcuts to start"}, "shortcuts": {"action": "Action", "clear_shortcut": "Clear Shortcut", "clear_topic": "Clear Messages", "copy_last_message": "Copy Last Message", "exit_fullscreen": "Exit Fullscreen", "key": "Key", "mini_window": "Quick Assistant", "new_topic": "New Topic", "press_shortcut": "Press Shortcut", "reset_defaults": "<PERSON><PERSON>s", "reset_defaults_confirm": "Are you sure you want to reset all shortcuts?", "reset_to_default": "Reset to De<PERSON>ult", "search_message": "Search Message", "search_message_in_chat": "Search Message in Current Chat", "show_app": "Show/Hide App", "show_settings": "Open Settings", "title": "Keyboard Shortcuts", "toggle_new_context": "Clear Context", "toggle_show_assistants": "Toggle Assistants", "toggle_show_topics": "Toggle Topics", "zoom_in": "Zoom In", "zoom_out": "Zoom Out", "zoom_reset": "Reset Zoom"}, "theme.system": "System", "theme.dark": "Dark", "theme.light": "Light", "theme.title": "Theme", "theme.color_primary": "Primary Color", "theme.window.style.opaque": "Opaque Window", "theme.window.style.title": "Window Style", "theme.window.style.transparent": "Transparent Window", "title": "Settings", "topic.position": "Topic position", "topic.position.left": "Left", "topic.position.right": "Right", "topic.show.time": "Show topic time", "topic.pin_to_top": "<PERSON>n <PERSON> to Top", "tray.onclose": "Minimize to Tray on Close", "tray.show": "Show Tray Icon", "tray.title": "Tray", "websearch": {"blacklist": "Blacklist", "blacklist_description": "Results from the following websites will not appear in search results", "blacklist_tooltip": "Please use the following format (separated by newlines)\nPattern matching: *://*.example.com/*\nRegular expression: /example\\.(net|org)/", "check": "Check", "check_failed": "Verification failed", "check_success": "Verification successful", "get_api_key": "Get API Key", "no_provider_selected": "Please select a search service provider before checking.", "search_max_result": "Number of search results", "search_provider": "Search service provider", "search_provider_placeholder": "Choose a search service provider.", "search_result_default": "<PERSON><PERSON><PERSON>", "search_with_time": "Search with dates included", "tavily": {"api_key": "Tavily API Key", "api_key.placeholder": "Enter Tavily API Key", "description": "Tavily is a search engine tailored for AI agents, delivering real-time, accurate results, intelligent query suggestions, and in-depth research capabilities.", "title": "<PERSON><PERSON>"}, "title": "Web Search", "subscribe": "Blacklist Subscription", "subscribe_update": "Update", "subscribe_add": "Add Subscription", "subscribe_url": "Subscription Url", "subscribe_name": "Alternative name", "subscribe_name.placeholder": "Alternative name used when the downloaded subscription feed has no name.", "subscribe_add_success": "Subscription feed added successfully!", "subscribe_delete": "Delete", "overwrite": "Override search service", "overwrite_tooltip": "Force use search service instead of LLM", "apikey": "API key", "free": "Free", "content_limit": "Content length limit", "content_limit_tooltip": "Limit the content length of the search results; content that exceeds the limit will be truncated."}, "quickPhrase": {"title": "Quick Phrases", "add": "Add Phrase", "edit": "Edit Phrase", "titleLabel": "Title", "contentLabel": "Content", "titlePlaceholder": "Please enter phrase title", "contentPlaceholder": "Please enter phrase content, support using variables, and press Tab to quickly locate the variable to modify. For example: \nHelp me plan a route from ${from} to ${to}, and send it to ${email}.", "delete": "Delete Phrase", "deleteConfirm": "The phrase cannot be recovered after deletion, continue?", "locationLabel": "Add Location", "global": "Global Phrases", "assistant": "Assistant Phrases"}, "quickPanel": {"title": "Quick Menu", "close": "Close", "select": "Select", "page": "Page", "confirm": "Confirm", "back": "Back", "forward": "Forward", "multiple": "Multiple Select"}, "privacy": {"title": "Privacy Settings", "enable_privacy_mode": "Anonymous reporting of errors and statistics"}, "zoom": {"title": "Page Zoom", "reset": "Reset"}, "openai": {"title": "OpenAI Settings", "summary_text_mode.title": "Summary Mode", "summary_text_mode.tip": "A summary of the reasoning performed by the model", "summary_text_mode.auto": "auto", "summary_text_mode.concise": "concise", "summary_text_mode.detailed": "detailed", "summary_text_mode.off": "off", "service_tier.title": "Service Tier", "service_tier.tip": "Specifies the latency tier to use for processing the request", "service_tier.auto": "auto", "service_tier.default": "default", "service_tier.flex": "flex"}}, "translate": {"any.language": "Any language", "target_language": "Target Language", "alter_language": "Alternative Language", "button.translate": "Translate", "close": "Close", "closed": "Translation closed", "copied": "Translation content copied", "detected.language": "Detected Language", "empty": "Translation content is empty", "not.found": "Translation content not found", "confirm": {"content": "Translation will replace the original text, continue?", "title": "Translation Confirmation"}, "error.failed": "Translation failed", "error.not_configured": "Translation model is not configured", "history": {"clear": "Clear History", "clear_description": "Clear history will delete all translation history, continue?", "delete": "Delete", "empty": "No translation history", "title": "Translation History"}, "input.placeholder": "Enter text to translate", "output.placeholder": "Translation", "processing": "Translation in progress...", "language.same": "Source and target languages are the same", "language.not_pair": "Source language is different from the set language", "settings": {"title": "Translation Settings", "model": "Model Settings", "model_desc": "Model used for translation service", "bidirectional": "Bidirectional Translation Settings", "bidirectional_tip": "When enabled, only bidirectional translation between source and target languages is supported", "scroll_sync": "<PERSON>roll Sync Settings"}, "title": "Translation", "tooltip.newline": "Newline", "menu": {"description": "Translate the content of the current input box"}}, "tray": {"quit": "Quit", "show_mini_window": "Quick Assistant", "show_window": "Show Window"}, "words": {"knowledgeGraph": "Knowledge Graph", "quit": "Quit", "show_window": "Show Window", "visualization": "Visualization"}, "update": {"title": "Update", "message": "New version {{version}} is ready, do you want to install it now?", "later": "Later", "install": "Install", "noReleaseNotes": "No release notes"}, "selection": {"name": "Selection Assistant", "action": {"builtin": {"translate": "Translate", "explain": "Explain", "summary": "Summarize", "search": "Search", "refine": "Refine", "copy": "Copy"}, "window": {"pin": "<PERSON>n", "pinned": "Pinned", "opacity": "Window Opacity", "original_show": "Show Original", "original_hide": "Hide Original", "original_copy": "Copy Original", "esc_close": "Esc: Close", "esc_stop": "Esc: Stop", "c_copy": "C: Copy", "r_regenerate": "R: Regenerate"}, "translate": {"smart_translate_tips": "Smart Translation: Content will be translated to the target language first; content already in the target language will be translated to the alternative language"}}, "settings": {"experimental": "Experimental Features", "enable": {"title": "Enable", "description": "Currently only supported on Windows systems"}, "toolbar": {"title": "<PERSON><PERSON><PERSON>", "trigger_mode": {"title": "Trigger Mode", "description": "Show toolbar immediately when text is selected, or show only when Ctrl key is held after selection.", "description_note": "The Ctrl key may not work in some apps. If you use AHK or other tools to remap the Ctrl key, it may not work.", "selected": "Selection", "ctrlkey": "Ctrl Key"}, "compact_mode": {"title": "Compact Mode", "description": "In compact mode, only icons are displayed without text"}}, "window": {"title": "Action Window", "follow_toolbar": {"title": "Follow <PERSON><PERSON><PERSON>", "description": "Window position will follow the toolbar. When disabled, it will always be centered."}, "remember_size": {"title": "Remember Size", "description": "Window will display at the last adjusted size during the application running"}, "auto_close": {"title": "Auto Close", "description": "Automatically close the window when it's not pinned and loses focus"}, "auto_pin": {"title": "Auto Pin", "description": "Pin the window by default"}, "opacity": {"title": "Opacity", "description": "Set the default opacity of the window, 100% is fully opaque"}}, "actions": {"title": "Actions", "reset": {"button": "Reset", "tooltip": "Reset to default actions. Custom actions will not be deleted.", "confirm": "Are you sure you want to reset to default actions? Custom actions will not be deleted."}, "add_tooltip": {"enabled": "Add Custom Action", "disabled": "Maximum number of custom actions reached ({{max}})"}, "delete_confirm": "Are you sure you want to delete this custom action?", "drag_hint": "Drag to reorder. Move above to enable action ({{enabled}}/{{max}})"}, "advanced": {"title": "Advanced", "filter_mode": {"title": "Application Filter", "description": "Can limit the selection assistant to only work in specific applications (whitelist) or not work (blacklist)", "default": "Off", "whitelist": "Whitelist", "blacklist": "Blacklist"}, "filter_list": {"title": "Filter List", "description": "Advanced feature, recommended for users with experience"}}, "user_modal": {"title": {"add": "Add Custom Action", "edit": "Edit Custom Action"}, "name": {"label": "Name", "hint": "Please enter action name"}, "icon": {"label": "Icon", "placeholder": "Enter Lucide icon name", "error": "Invalid icon name, please check your input", "tooltip": "Lucide icon names are lowercase, e.g. arrow-right", "view_all": "View All Icons", "random": "Random Icon"}, "model": {"label": "Model", "tooltip": "Using Assistant: Will use both the assistant's system prompt and model parameters", "default": "Default Model", "assistant": "Use Assistant"}, "assistant": {"label": "Select Assistant", "default": "<PERSON><PERSON><PERSON>"}, "prompt": {"label": "User Prompt", "tooltip": "User prompt serves as a supplement to user input and won't override the assistant's system prompt", "placeholder": "Use placeholder {{text}} to represent selected text. When empty, selected text will be appended to this prompt", "placeholder_text": "Placeholder", "copy_placeholder": "Copy Placeholder"}}, "search_modal": {"title": "Set Search Engine", "engine": {"label": "Search Engine", "custom": "Custom"}, "custom": {"name": {"label": "Custom Name", "hint": "Please enter search engine name", "max_length": "Name cannot exceed 16 characters"}, "url": {"label": "Custom Search URL", "hint": "Use {{queryString}} to represent the search term", "required": "Please enter search URL", "invalid_format": "Please enter a valid URL starting with http:// or https://", "missing_placeholder": "URL must contain {{queryString}} placeholder"}, "test": "Test"}}, "filter_modal": {"title": "Application Filter List", "user_tips": "Please enter the executable file name of the application, one per line, case insensitive, can be fuzzy matched. For example: chrome.exe, weixin.exe, Cherry Studio.exe, etc."}}}}}