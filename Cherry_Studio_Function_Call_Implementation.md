# Cherry Studio 让不支持 Function Call 的模型实现 Function Call 功能的技术方案

## 概述

Cherry Studio 是一个支持多种 LLM 提供商的桌面客户端，通过巧妙的技术设计，让原本不支持 Function Call 的模型也能够实现工具调用功能。本文档详细分析了其实现方案。

## 核心技术架构

### 1. 双模式工具调用系统

Cherry Studio 实现了两种工具调用模式：

#### 1.1 Function 模式（原生支持）
- **适用对象**：支持原生 Function Call 的模型（如 GPT-4、Claude、Gemini 等）
- **实现方式**：直接使用模型的原生 Function Call API
- **配置路径**：Assistant Settings → Tool Use Mode → "function"

#### 1.2 Prompt 模式（XML 格式模拟）
- **适用对象**：不支持原生 Function Call 的模型
- **实现方式**：通过 System Prompt + XML 格式指令模拟 Function Call
- **配置路径**：Assistant Settings → Tool Use Mode → "prompt"（默认模式）

### 2. 模型能力检测机制

```typescript
// 文件：src/renderer/src/config/models.ts
export function isFunctionCallingModel(model: Model): boolean {
  if (model.type?.includes('function_calling')) {
    return true
  }
  
  if (isEmbeddingModel(model)) {
    return false
  }
  
  // 特定提供商的模型检测
  if (model.provider === 'qiniu') {
    return ['deepseek-v3-tool', 'deepseek-v3-0324', 'qwq-32b', 'qwen2.5-72b-instruct'].includes(model.id)
  }
  
  if (['deepseek', 'anthropic'].includes(model.provider)) {
    return true
  }
  
  return FUNCTION_CALLING_REGEX.test(model.id)
}
```

支持原生 Function Call 的模型列表：
- GPT 系列：gpt-4o, gpt-4o-mini, gpt-4, gpt-4.5
- Claude 系列：claude-*
- Gemini 系列：gemini-*
- 其他：qwen, hunyuan, deepseek, glm-4, learnlm, grok-3 等

### 3. 工具调用模式选择逻辑

```typescript
// 文件：src/renderer/src/utils/mcp-tools.ts
export function isEnabledToolUse(assistant: Assistant) {
  if (assistant.model) {
    if (isFunctionCallingModel(assistant.model)) {
      return assistant.settings?.toolUseMode === 'function'
    }
  }
  return false
}
```

```typescript
// 文件：src/renderer/src/providers/AiProvider/BaseProvider.ts
protected setupToolsConfig<T>(params: { mcpTools?: MCPTool[]; model: Model; enableToolUse?: boolean }): {
  tools: T[]
} {
  const { mcpTools, model, enableToolUse } = params
  let tools: T[] = []
  
  if (!mcpTools?.length) {
    return { tools }
  }
  
  // 如果工具数量超过阈值，使用 System Prompt 模式
  if (mcpTools.length > BaseProvider.SYSTEM_PROMPT_THRESHOLD) {
    this.useSystemPromptForTools = true
    return { tools }
  }
  
  // 如果模型支持 Function Call 且启用了工具使用
  if (isFunctionCallingModel(model) && enableToolUse) {
    tools = this.convertMcpTools<T>(mcpTools)
    this.useSystemPromptForTools = false
  }
  
  return { tools }
}
```

## XML 格式 Function Call 实现详解

### 1. System Prompt 模板

```typescript
// 文件：src/renderer/src/utils/prompt.ts
export const SYSTEM_PROMPT = `In this environment you have access to a set of tools you can use to answer the user's question. \
You can use one tool per message, and will receive the result of that tool use in the user's response. You use tools step-by-step to accomplish a given task, with each tool use informed by the result of the previous tool use.

## Tool Use Formatting

Tool use is formatted using XML-style tags. The tool name is enclosed in opening and closing tags, and each parameter is similarly enclosed within its own set of tags. Here's the structure:

<tool_use>
  <n>{tool_name}</n>
  <arguments>{json_arguments}</arguments>
</tool_use>

The tool name should be the exact name of the tool you are using, and the arguments should be a JSON object containing the parameters required by that tool. For example:
<tool_use>
  <n>python_interpreter</n>
  <arguments>{"code": "5 + 3 + 1294.678"}</arguments>
</tool_use>

The user will respond with the result of the tool use, which should be formatted as follows:

<tool_use_result>
  <n>{tool_name}</n>
  <r>{result}</r>
</tool_use_result>

## Tool Use Examples
{{ TOOL_USE_EXAMPLES }}

## Tool Use Available Tools
{{ AVAILABLE_TOOLS }}

## Tool Use Rules
1. Always use the right arguments for the tools. Never use variable names as the action arguments, use the value instead.
2. Call a tool only when needed: do not call the search agent if you do not need information, try to solve the task yourself.
3. If no tool call is needed, just answer the question directly.
4. Never re-do a tool call that you previously did with the exact same parameters.
5. For tool use, MARK SURE use XML tag format as shown in the examples above. Do not use any other format.

# User Instructions
{{ USER_SYSTEM_PROMPT }}

Now Begin! If you solve the task correctly, you will receive a reward of $1,000,000.
`
```

### 2. 工具列表生成

```typescript
export const AvailableTools = (tools: MCPTool[]) => {
  const availableTools = tools
    .map((tool) => {
      return `
<tool>
  <n>${tool.id}</n>
  <description>${tool.description}</description>
  <arguments>
    ${tool.inputSchema ? JSON.stringify(tool.inputSchema) : ''}
  </arguments>
</tool>
`
    })
    .join('\n')
  return `<tools>
${availableTools}
</tools>`
}
```

### 3. XML 解析机制

```typescript
// 文件：src/renderer/src/utils/mcp-tools.ts
export function parseToolUse(content: string, mcpTools: MCPTool[]): ToolUseResponse[] {
  if (!content || !mcpTools || mcpTools.length === 0) {
    return []
  }
  
  // 正则表达式匹配 XML 格式的工具调用
  const toolUsePattern =
    /<tool_use>([\s\S]*?)<n>([\s\S]*?)<\/name>([\s\S]*?)<arguments>([\s\S]*?)<\/arguments>([\s\S]*?)<\/tool_use>/g
  
  const tools: ToolUseResponse[] = []
  let match
  let idx = 0
  
  // 查找所有工具使用块
  while ((match = toolUsePattern.exec(content)) !== null) {
    const toolName = match[2].trim()
    const toolArgs = match[4].trim()
    
    // 尝试解析参数为 JSON
    let parsedArgs
    try {
      parsedArgs = JSON.parse(toolArgs)
    } catch (error) {
      // 如果解析失败，使用字符串原样
      parsedArgs = toolArgs
    }
    
    const mcpTool = mcpTools.find((tool) => tool.id === toolName)
    if (!mcpTool) {
      Logger.error(`Tool "${toolName}" not found in MCP tools`)
      continue
    }
    
    // 添加到工具数组
    tools.push({
      id: `${toolName}-${idx++}`,
      toolUseId: mcpTool.id,
      tool: mcpTool,
      arguments: parsedArgs,
      status: 'pending'
    })
  }
  return tools
}
```

## 工具调用执行流程

### 1. 统一的工具调用处理

```typescript
export async function parseAndCallTools<R>(
  content: string | MCPToolResponse[],
  allToolResponses: MCPToolResponse[],
  onChunk: CompletionsParams['onChunk'],
  convertToMessage: (mcpToolResponse: MCPToolResponse, resp: MCPCallToolResponse, model: Model) => R | undefined,
  model: Model,
  mcpTools?: MCPTool[]
): Promise<R[]> {
  const toolResults: R[] = []
  let curToolResponses: MCPToolResponse[] = []
  
  if (Array.isArray(content)) {
    curToolResponses = content
  } else {
    // 处理工具使用（XML 解析）
    curToolResponses = parseToolUse(content, mcpTools || [])
  }
  
  if (!curToolResponses || curToolResponses.length === 0) {
    return toolResults
  }
  
  // 并行执行所有工具调用
  const toolPromises = curToolResponses.map(async (toolResponse) => {
    const toolCallResponse = await callMCPTool(toolResponse)
    return convertToMessage(toolResponse, toolCallResponse, model)
  })
  
  toolResults.push(...(await Promise.all(toolPromises)).filter((t) => typeof t !== 'undefined'))
  return toolResults
}
```

### 2. 不同提供商的处理方式

#### OpenAI Provider
```typescript
// 原生 Function Call 处理
const processToolCalls = async (mcpTools, toolCalls: ChatCompletionMessageToolCall[]) => {
  const mcpToolResponses = toolCalls
    .map((toolCall) => {
      const mcpTool = openAIToolsToMcpTool(mcpTools, toolCall)
      // ... 转换逻辑
    })
  return await parseAndCallTools(mcpToolResponses, ...)
}

// XML 格式处理
const processToolUses = async (content: string) => {
  return await parseAndCallTools(content, ...)
}
```

#### Gemini Provider
```typescript
const processToolCalls = async (toolCalls: FunctionCall[]) => {
  const mcpToolResponses: ToolCallResponse[] = toolCalls
    .map((toolCall) => {
      const mcpTool = geminiFunctionCallToMcpTool(mcpTools, toolCall)
      // ... 转换逻辑
    })
  return await parseAndCallTools(mcpToolResponses, ...)
}
```

## 关键技术特点

### 1. 自适应模式切换
- 系统自动检测模型是否支持原生 Function Call
- 根据模型能力自动选择最优的工具调用方式
- 用户可手动覆盖自动检测结果

### 2. 统一的 MCP 工具接口
- 所有工具都通过 Model Context Protocol (MCP) 标准化
- 不同格式的工具调用最终都转换为统一的 MCP 调用
- 支持多种工具服务器和协议

### 3. 智能阈值管理
- 当工具数量超过阈值时，自动切换到 System Prompt 模式
- 避免原生 Function Call 的 token 限制问题
- 保证大量工具场景下的稳定性

### 4. 错误处理和容错
- XML 解析失败时的优雅降级
- 工具调用错误的统一处理
- 详细的日志记录和调试信息

## 使用示例

### 配置工具调用模式
1. 打开 Assistant Settings
2. 找到 "Tool Use Mode" 设置
3. 选择 "function"（原生支持）或 "prompt"（XML 模拟）

### XML 格式工具调用示例
```xml
<tool_use>
  <n>search</n>
  <arguments>{"query": "Cherry Studio function calling"}</arguments>
</tool_use>
```

### 工具调用结果格式
```xml
<tool_use_result>
  <n>search</n>
  <r>搜索结果内容...</r>
</tool_use_result>
```

## 实现细节补充

### 1. 工具调用状态管理

```typescript
// 工具调用状态类型
export type ToolUseResponse = {
  id: string
  toolUseId: string
  tool: MCPTool
  arguments: any
  status: 'pending' | 'invoking' | 'done' | 'error'
  response?: MCPCallToolResponse
}

// 状态更新函数
function upsertMCPToolResponse(
  allToolResponses: MCPToolResponse[],
  toolResponse: MCPToolResponse,
  onChunk: CompletionsParams['onChunk']
) {
  // 更新工具调用状态并通知 UI
  onChunk({
    type: ChunkType.MCP_TOOL_IN_PROGRESS,
    toolResponse
  })
}
```

### 2. 多提供商适配器模式

Cherry Studio 使用适配器模式处理不同提供商的差异：

```typescript
// AihubmixProvider - 根据模型类型自动选择合适的提供商
export default class AihubmixProvider extends BaseProvider {
  private providers: Map<string, BaseProvider> = new Map()

  constructor(provider: Provider) {
    super(provider)

    // 初始化各个提供商
    this.providers.set('claude', new AnthropicProvider(provider))
    this.providers.set('gemini', new GeminiProvider(provider))
    this.providers.set('openai', new OpenAIResponseProvider(provider))
    this.providers.set('default', new OpenAIProvider(provider))
  }

  private getProvider(model: Model): BaseProvider {
    const id = model.id.toLowerCase()
    if (id.startsWith('claude')) {
      return this.providers.get('claude')!
    }
    if (id.startsWith('gemini')) {
      return this.providers.get('gemini')!
    }
    return this.defaultProvider
  }
}
```

### 3. 工具调用结果转换

不同提供商需要将 MCP 工具调用结果转换为各自的消息格式：

```typescript
// OpenAI 格式转换
public mcpToolCallResponseToMessage = (mcpToolResponse: MCPToolResponse, resp: MCPCallToolResponse, model: Model) => {
  if ('toolCallId' in mcpToolResponse && mcpToolResponse.toolCallId) {
    const toolCallOut: ChatCompletionToolMessageParam = {
      role: 'tool',
      tool_call_id: mcpToolResponse.toolCallId,
      content: JSON.stringify(resp.content)
    }
    return toolCallOut
  }
}

// Anthropic 格式转换
public mcpToolCallResponseToMessage = (mcpToolResponse: MCPToolResponse, resp: MCPCallToolResponse, model: Model) => {
  return {
    role: 'user',
    content: [
      {
        type: 'tool_result',
        tool_use_id: mcpToolResponse.toolCallId!,
        content: resp.content.map((item) => {
          if (item.type === 'text') {
            return { type: 'text', text: item.text || '' }
          }
          // 处理其他类型...
        })
      }
    ]
  }
}
```

### 4. 错误处理机制

```typescript
export async function callMCPTool(toolResponse: MCPToolResponse): Promise<MCPCallToolResponse> {
  try {
    const server = getMcpServerByTool(toolResponse.tool)
    if (!server) {
      throw new Error(`Server not found: ${toolResponse.tool.serverName}`)
    }

    const resp = await window.api.mcp.callTool({
      server,
      name: toolResponse.tool.name,
      args: toolResponse.arguments
    })

    return resp
  } catch (e) {
    console.error(`[MCP] Error calling Tool: ${toolResponse.tool.serverName} ${toolResponse.tool.name}`, e)
    return Promise.resolve({
      isError: true,
      content: [
        {
          type: 'text',
          text: `Error calling tool ${toolResponse.tool.name}: ${e instanceof Error ? e.stack || e.message || 'No error details available' : JSON.stringify(e)}`
        }
      ]
    })
  }
}
```

### 5. 性能优化策略

#### 5.1 工具数量阈值管理
```typescript
// BaseProvider.ts
static readonly SYSTEM_PROMPT_THRESHOLD = 10 // 工具数量阈值

protected setupToolsConfig<T>(params: { mcpTools?: MCPTool[]; model: Model; enableToolUse?: boolean }) {
  // 如果工具数量超过阈值，强制使用 System Prompt 模式
  if (mcpTools.length > BaseProvider.SYSTEM_PROMPT_THRESHOLD) {
    this.useSystemPromptForTools = true
    return { tools: [] }
  }
}
```

#### 5.2 并行工具调用
```typescript
// 并行执行多个工具调用以提高性能
const toolPromises = curToolResponses.map(async (toolResponse) => {
  const toolCallResponse = await callMCPTool(toolResponse)
  return convertToMessage(toolResponse, toolCallResponse, model)
})

toolResults.push(...(await Promise.all(toolPromises)).filter((t) => typeof t !== 'undefined'))
```

### 6. 调试和监控

```typescript
// 详细的日志记录
Logger.log(`[MCP] Calling Tool: ${toolResponse.tool.serverName} ${toolResponse.tool.name}`, toolResponse.tool)
Logger.log(`[MCP] Tool called: ${toolResponse.tool.serverName} ${toolResponse.tool.name}`, resp)

// 工具调用失败时的错误日志
Logger.error(`Tool "${toolName}" not found in MCP tools`)
```

## 配置和使用指南

### 1. 助手设置中的工具调用配置

在助手设置界面，用户可以配置：
- **Tool Use Mode**: 选择 "prompt" 或 "function"
- **Temperature**: 控制模型创造性
- **Context Count**: 上下文消息数量
- **Max Tokens**: 最大输出 token 数

### 2. MCP 服务器管理

Cherry Studio 支持多种 MCP 服务器：
- **内置服务器**: 文件系统、内存管理等
- **外部服务器**: 通过 stdio、HTTP、SSE 等协议连接
- **工具过滤**: 可以禁用特定工具

### 3. 最佳实践建议

1. **模型选择**:
   - 对于支持原生 Function Call 的模型，优先使用 "function" 模式
   - 对于不支持的模型，使用 "prompt" 模式

2. **工具数量管理**:
   - 控制同时启用的工具数量，避免超过阈值
   - 根据任务需求选择性启用工具

3. **错误处理**:
   - 监控工具调用状态
   - 处理网络超时和服务器错误

## 技术优势

### 1. 兼容性
- 支持几乎所有主流 LLM 模型
- 统一的工具调用接口
- 向后兼容性保证

### 2. 可扩展性
- 插件化的 MCP 服务器架构
- 易于添加新的工具和服务
- 支持自定义工具开发

### 3. 性能
- 智能的模式选择
- 并行工具调用
- 资源使用优化

### 4. 用户体验
- 透明的工具调用过程
- 实时状态反馈
- 详细的错误信息

## 总结

Cherry Studio 通过以下技术手段成功让不支持 Function Call 的模型实现了工具调用功能：

1. **双模式架构**：原生 Function Call + XML 格式模拟
2. **智能检测**：自动识别模型能力并选择最优模式
3. **统一接口**：MCP 协议统一不同格式的工具调用
4. **XML 解析**：正则表达式解析 XML 格式的工具调用指令
5. **System Prompt**：详细的提示词指导模型生成正确的 XML 格式
6. **适配器模式**：处理不同提供商的 API 差异
7. **状态管理**：完整的工具调用生命周期管理
8. **错误处理**：健壮的错误处理和恢复机制

这种设计既保证了支持原生 Function Call 模型的最佳性能，又为不支持的模型提供了可靠的替代方案，实现了广泛的模型兼容性和优秀的用户体验。
