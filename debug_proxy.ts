#!/usr/bin/env deno run --allow-net --allow-env

/**
 * 调试版本 - 输出详细的请求和响应信息
 */

import { serve } from "https://deno.land/std@0.208.0/http/server.ts";

interface OpenAIMessage {
  role: "system" | "user" | "assistant" | "tool";
  content: string | null | any[];
  tool_calls?: any[];
  tool_call_id?: string;
  name?: string;
}

interface ChatCompletionRequest {
  model: string;
  messages: OpenAIMessage[];
  tools?: any[];
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
}

class DebugProxy {
  private parseUpstreamUrl(requestUrl: string): string {
    const url = new URL(requestUrl);
    const upstreamUrl = url.pathname.substring(1);
    
    if (upstreamUrl.startsWith('http://') || upstreamUrl.startsWith('https://')) {
      return upstreamUrl.replace(/\/v1\/chat\/completions$/, '');
    }
    
    throw new Error('Invalid upstream URL format');
  }

  private extractApiKey(headers: Headers): string {
    const authHeader = headers.get('Authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      throw new Error('Invalid Authorization header');
    }
    return authHeader.substring(7);
  }

  async handleRequest(request: ChatCompletionRequest, requestUrl: string, requestHeaders: Headers): Promise<Response> {
    try {
      const upstreamUrl = this.parseUpstreamUrl(requestUrl);
      const apiKey = this.extractApiKey(requestHeaders);
      
      console.log('\n🔍 === DEBUG INFO ===');
      console.log('📡 Upstream URL:', upstreamUrl);
      console.log('🔑 API Key prefix:', apiKey.substring(0, 10) + '...');
      console.log('📝 Original messages count:', request.messages.length);
      console.log('🛠️  Tools count:', request.tools?.length || 0);
      
      // 详细打印每条消息
      console.log('\n📋 Original Messages:');
      request.messages.forEach((msg, i) => {
        console.log(`  ${i + 1}. [${msg.role}]`);
        if (typeof msg.content === 'string') {
          console.log(`     Content: ${msg.content.substring(0, 200)}${msg.content.length > 200 ? '...' : ''}`);
        } else {
          console.log(`     Content: ${JSON.stringify(msg.content).substring(0, 200)}...`);
        }
        if (msg.tool_calls) {
          console.log(`     Tool calls: ${msg.tool_calls.length}`);
        }
        if (msg.tool_call_id) {
          console.log(`     Tool call ID: ${msg.tool_call_id}`);
        }
        if (msg.name) {
          console.log(`     Name: ${msg.name}`);
        }
      });

      // 直接转发原始请求，不做任何转换
      console.log('\n📤 Sending original request to upstream...');
      
      const upstreamResponse = await fetch(`${upstreamUrl}/v1/chat/completions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
        },
        body: JSON.stringify(request),
      });

      console.log('📥 Response status:', upstreamResponse.status);
      
      if (!upstreamResponse.ok) {
        const errorText = await upstreamResponse.text();
        console.error('❌ Upstream error:', errorText);
        return new Response(errorText, {
          status: upstreamResponse.status,
          headers: { "Content-Type": "application/json" },
        });
      }

      const responseData = await upstreamResponse.json();
      
      console.log('📋 Response structure:');
      console.log('  Choices:', responseData.choices?.length || 0);
      if (responseData.choices?.[0]?.message) {
        const msg = responseData.choices[0].message;
        console.log('  Message role:', msg.role);
        console.log('  Content length:', msg.content?.length || 0);
        console.log('  Content preview:', msg.content?.substring(0, 100) || 'null');
        console.log('  Has tool calls:', !!msg.tool_calls);
      }
      
      console.log('🔍 === END DEBUG ===\n');

      return new Response(JSON.stringify(responseData), {
        headers: { "Content-Type": "application/json" },
      });

    } catch (error) {
      console.error("❌ Error:", error);
      return new Response(JSON.stringify({
        error: {
          message: error instanceof Error ? error.message : "Internal server error",
          type: "internal_error"
        }
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  }
}

async function main() {
  const port = parseInt(Deno.env.get("PORT") || "8000");
  const proxy = new DebugProxy();

  console.log(`🐛 Debug Proxy Server starting on port ${port}`);
  console.log(`📖 This version forwards requests without transformation for debugging`);

  await serve(async (req: Request) => {
    const url = new URL(req.url);
    
    if (req.method === "OPTIONS") {
      return new Response(null, {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        },
      });
    }

    if (url.pathname === "/health") {
      return new Response(JSON.stringify({ status: "healthy", mode: "debug" }), {
        headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" },
      });
    }

    if (url.pathname.endsWith("/v1/chat/completions") && req.method === "POST") {
      try {
        const requestData = await req.json() as ChatCompletionRequest;
        const response = await proxy.handleRequest(requestData, req.url, req.headers);
        
        const headers = new Headers(response.headers);
        headers.set("Access-Control-Allow-Origin", "*");
        
        return new Response(response.body, {
          status: response.status,
          headers,
        });
      } catch (error) {
        console.error("❌ Error parsing request:", error);
        return new Response(JSON.stringify({
          error: { message: "Invalid JSON in request body" }
        }), {
          status: 400,
          headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" },
        });
      }
    }

    return new Response(JSON.stringify({
      error: { message: "Not Found" }
    }), { 
      status: 404,
      headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
    });
  }, { port });
}

if (import.meta.main) {
  main();
}
