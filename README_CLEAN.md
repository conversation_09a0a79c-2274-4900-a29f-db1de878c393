# OpenAI Function Call Proxy

让任意模型都支持 OpenAI Function Call 功能的轻量级代理服务器。

## 🚀 快速开始

### 启动服务器
```bash
# 方式1: 使用启动脚本
chmod +x start_clean.sh
./start_clean.sh

# 方式2: 直接运行
deno run --allow-net --allow-env simple_function_call_proxy_clean.ts
```

### 使用代理
URL格式：`http://localhost:8000/{upstream_url}`

```bash
# OpenAI
curl -X POST http://localhost:8000/https://api.openai.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-openai-key" \
  -d '{
    "model": "gpt-4",
    "messages": [{"role": "user", "content": "What is 15 * 23?"}],
    "tools": [{
      "type": "function",
      "function": {
        "name": "calculate",
        "description": "Perform mathematical calculations",
        "parameters": {
          "type": "object",
          "properties": {"expression": {"type": "string"}},
          "required": ["expression"]
        }
      }
    }]
  }'

# Claude
curl -X POST http://localhost:8000/https://api.anthropic.com/v1/chat/completions \
  -H "Authorization: Bearer sk-ant-your-claude-key" \
  -d '{...}'

# 本地服务
curl -X POST http://localhost:8000/http://localhost:11434/v1/chat/completions \
  -H "Authorization: Bearer ollama" \
  -d '{...}'
```

## 🔧 工作原理

1. **URL解析**: 从请求URL提取上游服务地址
2. **认证提取**: 从Authorization头提取API密钥
3. **消息转换**: 将tools转换为XML格式的System Prompt
4. **请求代理**: 转发到上游服务
5. **响应解析**: 解析XML工具调用，转换为标准格式

### XML格式
模型需要按以下格式生成工具调用：
```xml
<tool_use>
  <name>function_name</name>
  <arguments>{"param1": "value1"}</arguments>
</tool_use>
```

## 📋 支持的内容格式

- **文本消息**: `{"role": "user", "content": "Hello"}`
- **多模态消息**: 支持图片、音频等OpenAI v1 API格式
- **工具调用**: 完整的Function Call流程

## 🔍 API端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/health` | GET | 健康检查 |
| `/` | GET | API信息 |
| `/{upstream_url}` | POST | 代理聊天请求 |

## ⚙️ 配置

| 环境变量 | 描述 | 默认值 |
|----------|------|--------|
| `PORT` | 服务器端口 | `8000` |

## 🐳 Docker部署

```dockerfile
FROM denoland/deno:1.40.2
WORKDIR /app
COPY simple_function_call_proxy_clean.ts .
RUN deno cache simple_function_call_proxy_clean.ts
EXPOSE 8000
CMD ["deno", "run", "--allow-net", "--allow-env", "simple_function_call_proxy_clean.ts"]
```

## 📊 特性

- ✅ 支持任意OpenAI兼容API
- ✅ 多模态内容支持
- ✅ 动态路由和认证
- ✅ 轻量级，~300行代码
- ✅ 零配置，开箱即用

## 📄 许可证

MIT License
