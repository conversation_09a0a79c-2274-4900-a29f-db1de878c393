#!/bin/bash

# Function Call Proxy 启动脚本 (精简版)

set -e

# 检查Deno
if ! command -v deno &> /dev/null; then
    echo "⚠️  Deno 未安装，正在安装..."
    curl -fsSL https://deno.land/x/install/install.sh | sh
    export PATH="$HOME/.deno/bin:$PATH"
fi

# 启动服务器
PORT=${PORT:-8000}

echo "🚀 启动 Function Call Proxy..."
echo "📡 端口: $PORT"
echo "📖 格式: /{upstream_url}"
echo "📝 示例: http://localhost:$PORT/https://api.openai.com/v1/chat/completions"
echo ""

deno run --allow-net --allow-env simple_function_call_proxy_clean.ts
