#!/usr/bin/env deno run --allow-net

/**
 * 使用示例：测试Function Call代理服务器
 */

interface TestCase {
  name: string;
  messages: any[];
  tools?: any[];
  expectedToolCalls?: string[];
}

const testCases: TestCase[] = [
  {
    name: "简单工具调用 - 获取天气",
    messages: [
      {
        role: "user",
        content: "What's the weather like in Tokyo?"
      }
    ],
    tools: [
      {
        type: "function",
        function: {
          name: "get_weather",
          description: "Get current weather information for a location",
          parameters: {
            type: "object",
            properties: {
              location: {
                type: "string",
                description: "The city name"
              },
              unit: {
                type: "string",
                enum: ["celsius", "fahrenheit"],
                description: "Temperature unit"
              }
            },
            required: ["location"]
          }
        }
      }
    ],
    expectedToolCalls: ["get_weather"]
  },
  {
    name: "多步骤工具调用 - 计算和转换",
    messages: [
      {
        role: "user",
        content: "Calculate 15 * 23 and then convert the result to binary"
      }
    ],
    tools: [
      {
        type: "function",
        function: {
          name: "calculate",
          description: "Perform mathematical calculations",
          parameters: {
            type: "object",
            properties: {
              expression: {
                type: "string",
                description: "Mathematical expression to evaluate"
              }
            },
            required: ["expression"]
          }
        }
      },
      {
        type: "function",
        function: {
          name: "convert_base",
          description: "Convert numbers between different bases",
          parameters: {
            type: "object",
            properties: {
              number: {
                type: "number",
                description: "Number to convert"
              },
              from_base: {
                type: "number",
                description: "Source base (2-36)"
              },
              to_base: {
                type: "number",
                description: "Target base (2-36)"
              }
            },
            required: ["number", "from_base", "to_base"]
          }
        }
      }
    ],
    expectedToolCalls: ["calculate", "convert_base"]
  },
  {
    name: "工具调用结果处理",
    messages: [
      {
        role: "user",
        content: "What's the weather in Tokyo?"
      },
      {
        role: "assistant",
        content: "I'll check the weather in Tokyo for you.",
        tool_calls: [
          {
            id: "call_123",
            type: "function",
            function: {
              name: "get_weather",
              arguments: '{"location": "Tokyo", "unit": "celsius"}'
            }
          }
        ]
      },
      {
        role: "tool",
        tool_call_id: "call_123",
        name: "get_weather",
        content: '{"temperature": 22, "condition": "sunny", "humidity": 65}'
      },
      {
        role: "user",
        content: "That's nice! What about the weather in London?"
      }
    ],
    tools: [
      {
        type: "function",
        function: {
          name: "get_weather",
          description: "Get current weather information for a location",
          parameters: {
            type: "object",
            properties: {
              location: { type: "string" },
              unit: { type: "string", enum: ["celsius", "fahrenheit"] }
            },
            required: ["location"]
          }
        }
      }
    ],
    expectedToolCalls: ["get_weather"]
  }
];

async function testProxy(baseUrl: string = "http://localhost:8000") {
  console.log("🧪 开始测试Function Call代理服务器");
  console.log(`📡 代理服务器地址: ${baseUrl}`);
  console.log("=" .repeat(60));

  // 测试不同的URL格式
  const urlFormats = [
    `${baseUrl}/proxy/openai/v1/chat/completions`,
    `${baseUrl}/openai/v1/chat/completions`,
    `${baseUrl}/v1/chat/completions?upstream=https://api.openai.com`
  ];

  for (let formatIndex = 0; formatIndex < urlFormats.length; formatIndex++) {
    const testUrl = urlFormats[formatIndex];
    console.log(`\n🌐 测试URL格式 ${formatIndex + 1}: ${testUrl}`);

    for (const testCase of testCases) {
      console.log(`\n🔍 测试用例: ${testCase.name}`);
      console.log("-".repeat(40));

      try {
        const response = await fetch(testUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": "Bearer test-key"
          },
          body: JSON.stringify({
            model: "gpt-3.5-turbo",
            messages: testCase.messages,
            tools: testCase.tools,
            temperature: 0.7,
            max_tokens: 1000
          })
        });

      if (!response.ok) {
        console.error(`❌ HTTP错误: ${response.status}`);
        console.error(await response.text());
        continue;
      }

      const result = await response.json();
      
      console.log("📤 请求消息:");
      testCase.messages.forEach((msg, i) => {
        console.log(`  ${i + 1}. [${msg.role}] ${msg.content?.substring(0, 100)}${msg.content?.length > 100 ? '...' : ''}`);
        if (msg.tool_calls) {
          console.log(`     工具调用: ${msg.tool_calls.map(tc => tc.function.name).join(', ')}`);
        }
      });

      console.log("\n📥 响应结果:");
      if (result.choices && result.choices[0]) {
        const message = result.choices[0].message;
        console.log(`  角色: ${message.role}`);
        if (message.content) {
          console.log(`  内容: ${message.content.substring(0, 200)}${message.content.length > 200 ? '...' : ''}`);
        }
        if (message.tool_calls) {
          console.log(`  工具调用 (${message.tool_calls.length}个):`);
          message.tool_calls.forEach((tc, i) => {
            console.log(`    ${i + 1}. ${tc.function.name}(${tc.function.arguments})`);
          });
        }
      }

      // 验证预期的工具调用
      if (testCase.expectedToolCalls) {
        const actualToolCalls = result.choices?.[0]?.message?.tool_calls?.map(tc => tc.function.name) || [];
        const hasExpectedTools = testCase.expectedToolCalls.some(expected => 
          actualToolCalls.includes(expected)
        );
        
        if (hasExpectedTools) {
          console.log("✅ 工具调用验证通过");
        } else {
          console.log(`⚠️  预期工具调用: ${testCase.expectedToolCalls.join(', ')}`);
          console.log(`   实际工具调用: ${actualToolCalls.join(', ')}`);
        }
      }

    } catch (error) {
      console.error(`❌ 测试失败: ${error.message}`);
    }
    }
  }

  console.log("\n" + "=".repeat(60));
  console.log("🎉 测试完成!");
}

// 模拟工具执行的简单示例
async function simulateToolExecution() {
  console.log("\n🔧 模拟完整的工具调用流程");
  console.log("-".repeat(40));

  const baseUrl = "http://localhost:8000";
  
  // 第一步：发送需要工具调用的消息
  console.log("1️⃣ 发送用户消息...");
  const step1Response = await fetch(`${baseUrl}/v1/chat/completions`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": "Bearer test-key"
    },
    body: JSON.stringify({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "user", content: "What's the current time in Tokyo?" }
      ],
      tools: [
        {
          type: "function",
          function: {
            name: "get_current_time",
            description: "Get current time in a specific timezone",
            parameters: {
              type: "object",
              properties: {
                timezone: { type: "string", description: "Timezone name" }
              },
              required: ["timezone"]
            }
          }
        }
      ]
    })
  });

  const step1Result = await step1Response.json();
  console.log("📤 助手响应包含工具调用:");
  if (step1Result.choices?.[0]?.message?.tool_calls) {
    step1Result.choices[0].message.tool_calls.forEach(tc => {
      console.log(`   ${tc.function.name}(${tc.function.arguments})`);
    });
  }

  // 第二步：模拟工具执行结果
  if (step1Result.choices?.[0]?.message?.tool_calls) {
    console.log("\n2️⃣ 模拟工具执行...");
    const toolCall = step1Result.choices[0].message.tool_calls[0];
    const mockResult = {
      timezone: "Asia/Tokyo",
      current_time: "2024-01-15 14:30:00 JST",
      timestamp: Date.now()
    };
    
    console.log(`🔧 工具 ${toolCall.function.name} 执行结果:`, JSON.stringify(mockResult));

    // 第三步：发送工具结果，获取最终回复
    console.log("\n3️⃣ 发送工具结果，获取最终回复...");
    const step3Response = await fetch(`${baseUrl}/v1/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer test-key"
      },
      body: JSON.stringify({
        model: "gpt-3.5-turbo",
        messages: [
          { role: "user", content: "What's the current time in Tokyo?" },
          step1Result.choices[0].message,
          {
            role: "tool",
            tool_call_id: toolCall.id,
            name: toolCall.function.name,
            content: JSON.stringify(mockResult)
          }
        ],
        tools: [
          {
            type: "function",
            function: {
              name: "get_current_time",
              description: "Get current time in a specific timezone",
              parameters: {
                type: "object",
                properties: {
                  timezone: { type: "string" }
                },
                required: ["timezone"]
              }
            }
          }
        ]
      })
    });

    const step3Result = await step3Response.json();
    console.log("📥 最终回复:");
    console.log(`   ${step3Result.choices?.[0]?.message?.content}`);
  }
}

// 主函数
async function main() {
  const args = Deno.args;
  const command = args[0] || "test";
  const baseUrl = args[1] || "http://localhost:8000";

  switch (command) {
    case "test":
      await testProxy(baseUrl);
      break;
    case "simulate":
      await simulateToolExecution();
      break;
    case "help":
      console.log(`
使用方法:
  deno run --allow-net example_usage.ts [command] [base_url]

命令:
  test      - 运行基本测试用例 (默认)
  simulate  - 模拟完整的工具调用流程
  help      - 显示帮助信息

示例:
  deno run --allow-net example_usage.ts test
  deno run --allow-net example_usage.ts simulate
  deno run --allow-net example_usage.ts test http://localhost:8000
      `);
      break;
    default:
      console.error(`未知命令: ${command}`);
      console.log("使用 'help' 查看可用命令");
  }
}

if (import.meta.main) {
  main();
}
