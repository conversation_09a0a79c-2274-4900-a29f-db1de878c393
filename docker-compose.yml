version: '3.8'

services:
  function-call-proxy:
    build: .
    ports:
      - "8000:8000"
    environment:
      - PORT=8000
      - UPSTREAM_URL=${UPSTREAM_URL:-https://api.openai.com}
      - API_KEY=${API_KEY}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选：添加nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - function-call-proxy
    restart: unless-stopped
    profiles:
      - with-nginx

networks:
  default:
    name: function-call-proxy-network
