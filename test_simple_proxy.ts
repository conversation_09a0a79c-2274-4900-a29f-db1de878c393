#!/usr/bin/env deno run --allow-net

/**
 * 测试简化版Function Call代理服务器
 */

async function testSimpleProxy() {
  console.log("🧪 测试简化版Function Call代理服务器");
  console.log("=" .repeat(50));

  const baseUrl = "http://localhost:8000";

  // 1. 健康检查
  console.log("\n🏥 健康检查");
  console.log("-".repeat(30));
  
  try {
    const healthResponse = await fetch(`${baseUrl}/health`);
    const healthData = await healthResponse.json();
    console.log("✅ 健康检查通过");
    console.log(`   状态: ${healthData.status}`);
    console.log(`   版本: ${healthData.version}`);
  } catch (error) {
    console.error("❌ 健康检查失败:", error.message);
    return;
  }

  // 2. API信息
  console.log("\n📖 API信息");
  console.log("-".repeat(30));
  
  try {
    const infoResponse = await fetch(`${baseUrl}/`);
    const infoData = await infoResponse.json();
    console.log("✅ API信息获取成功");
    console.log(`   名称: ${infoData.name}`);
    console.log(`   格式: ${infoData.format}`);
    console.log(`   示例: ${infoData.example}`);
  } catch (error) {
    console.error("❌ API信息获取失败:", error.message);
  }

  // 3. 测试不同的上游服务
  const testCases = [
    {
      name: "OpenAI API",
      url: `${baseUrl}/https://api.openai.com/v1/chat/completions`,
      apiKey: "sk-test-openai-key"
    },
    {
      name: "Anthropic API", 
      url: `${baseUrl}/https://api.anthropic.com/v1/chat/completions`,
      apiKey: "sk-ant-test-key"
    },
    {
      name: "本地Ollama",
      url: `${baseUrl}/http://localhost:11434/v1/chat/completions`,
      apiKey: "ollama"
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n🔍 测试: ${testCase.name}`);
    console.log("-".repeat(30));
    console.log(`📡 URL: ${testCase.url}`);

    try {
      const response = await fetch(testCase.url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${testCase.apiKey}`
        },
        body: JSON.stringify({
          model: "gpt-3.5-turbo",
          messages: [
            { role: "user", content: "What's 2+2? Use a calculator if needed." }
          ],
          tools: [
            {
              type: "function",
              function: {
                name: "calculate",
                description: "Perform mathematical calculations",
                parameters: {
                  type: "object",
                  properties: {
                    expression: {
                      type: "string",
                      description: "Mathematical expression to evaluate"
                    }
                  },
                  required: ["expression"]
                }
              }
            }
          ]
        })
      });

      console.log(`📊 状态码: ${response.status}`);
      
      if (response.ok) {
        const result = await response.json();
        
        if (result.choices && result.choices[0]) {
          const message = result.choices[0].message;
          console.log(`✅ 响应格式正确`);
          console.log(`   角色: ${message.role}`);
          
          if (message.content) {
            console.log(`   内容: ${message.content.substring(0, 100)}...`);
          }
          
          if (message.tool_calls) {
            console.log(`   工具调用: ${message.tool_calls.length}个`);
            message.tool_calls.forEach((tc: any, i: number) => {
              console.log(`     ${i + 1}. ${tc.function.name}(${tc.function.arguments})`);
            });
          }
        }
      } else {
        const errorText = await response.text();
        console.log(`❌ 请求失败`);
        console.log(`   错误: ${errorText.substring(0, 200)}...`);
      }

    } catch (error) {
      console.error(`❌ 测试失败: ${error.message}`);
    }
  }

  // 4. 测试多模态内容
  console.log(`\n🖼️  测试多模态内容`);
  console.log("-".repeat(30));

  try {
    const response = await fetch(`${baseUrl}/https://api.openai.com/v1/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-test-key"
      },
      body: JSON.stringify({
        model: "gpt-4-vision-preview",
        messages: [
          {
            role: "user",
            content: [
              { type: "text", text: "What's in this image? Use image analysis if needed." },
              {
                type: "image_url",
                image_url: {
                  url: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2ODApLCBxdWFsaXR5ID0gOTAK/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwKDAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU/9sAQwEDBAQFBAUJBQUJFA0LDRQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU/8AAEQgAAQABAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBkQgUobHB0fAjM+HxFQkSQlJicvEzNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9/KKKKAP/2Q==",
                  detail: "auto"
                }
              }
            ]
          }
        ],
        tools: [
          {
            type: "function",
            function: {
              name: "analyze_image",
              description: "Analyze image content and describe what's visible",
              parameters: {
                type: "object",
                properties: {
                  description: {
                    type: "string",
                    description: "Description of what's in the image"
                  }
                },
                required: ["description"]
              }
            }
          }
        ]
      })
    });

    console.log(`📊 状态码: ${response.status}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log("✅ 多模态内容处理成功");
      if (result.choices?.[0]?.message) {
        const message = result.choices[0].message;
        console.log(`   内容类型: ${typeof message.content}`);
        if (message.tool_calls) {
          console.log(`   工具调用: ${message.tool_calls.length}个`);
        }
      }
    } else {
      const errorText = await response.text();
      console.log(`❌ 多模态内容处理失败`);
      console.log(`   错误: ${errorText.substring(0, 200)}...`);
    }

  } catch (error) {
    console.error(`❌ 多模态测试失败: ${error.message}`);
  }

  console.log("\n" + "=".repeat(50));
  console.log("🎉 测试完成!");
}

// URL解析测试
async function testUrlParsing() {
  console.log("\n🔗 URL解析测试");
  console.log("-".repeat(30));

  const testUrls = [
    "http://localhost:8000/https://api.openai.com/v1/chat/completions",
    "http://localhost:8000/https://api.anthropic.com/v1/chat/completions",
    "http://localhost:8000/http://localhost:11434/v1/chat/completions",
    "http://localhost:8000/https://custom-api.example.com/v1/chat/completions"
  ];

  for (const testUrl of testUrls) {
    console.log(`🔍 测试URL: ${testUrl}`);
    
    try {
      const response = await fetch(testUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer test-key"
        },
        body: JSON.stringify({
          model: "test",
          messages: [{ role: "user", content: "test" }]
        })
      });
      
      console.log(`   状态码: ${response.status}`);
      
      if (!response.ok) {
        const errorText = await response.text();
        // 检查是否是URL解析错误还是上游服务错误
        if (errorText.includes("Invalid upstream URL format")) {
          console.log(`   ❌ URL解析失败`);
        } else {
          console.log(`   ✅ URL解析成功，上游服务响应`);
        }
      }
    } catch (error) {
      console.log(`   网络错误: ${error.message}`);
    }
  }
}

// 主函数
async function main() {
  const args = Deno.args;
  const command = args[0] || "all";

  switch (command) {
    case "all":
      await testSimpleProxy();
      await testUrlParsing();
      break;
    case "health":
      const response = await fetch("http://localhost:8000/health");
      console.log(await response.json());
      break;
    case "info":
      const infoResponse = await fetch("http://localhost:8000/");
      console.log(await infoResponse.json());
      break;
    case "url":
      await testUrlParsing();
      break;
    case "help":
      console.log(`
使用方法:
  deno run --allow-net test_simple_proxy.ts [command]

命令:
  all     - 运行所有测试 (默认)
  health  - 健康检查
  info    - API信息
  url     - URL解析测试
  help    - 显示帮助
      `);
      break;
    default:
      console.error(`未知命令: ${command}`);
      console.log("使用 'help' 查看可用命令");
  }
}

if (import.meta.main) {
  main();
}
