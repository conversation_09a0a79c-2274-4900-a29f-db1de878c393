# 部署指南

本指南将帮助你部署OpenAI Function Call代理服务器。

## 📋 前置要求

### 本地部署
- [Deno](https://deno.land/) 1.40.0+
- 有效的API密钥（OpenAI、<PERSON>、或其他兼容服务）

### Docker部署
- [Docker](https://www.docker.com/) 20.0+
- [Docker Compose](https://docs.docker.com/compose/) 1.29+ (可选)

## 🚀 快速部署

### 方法1: 使用启动脚本（推荐）

```bash
# 1. 克隆或下载文件
# 2. 设置环境变量
export API_KEY="your-api-key-here"
export UPSTREAM_URL="https://api.openai.com"  # 可选
export PORT="8000"  # 可选

# 3. 运行启动脚本
chmod +x start.sh
./start.sh start
```

### 方法2: 直接使用Deno

```bash
# 设置环境变量
export API_KEY="your-api-key-here"
export UPSTREAM_URL="https://api.openai.com"
export PORT="8000"

# 启动服务器
deno run --allow-net --allow-env openai_function_call_proxy.ts
```

### 方法3: Docker部署

```bash
# 构建镜像
docker build -t function-call-proxy .

# 运行容器
docker run -d \
  --name function-call-proxy \
  -p 8000:8000 \
  -e API_KEY="your-api-key-here" \
  -e UPSTREAM_URL="https://api.openai.com" \
  function-call-proxy
```

### 方法4: Docker Compose部署

```bash
# 创建环境变量文件
cat > .env << EOF
API_KEY=your-api-key-here
UPSTREAM_URL=https://api.openai.com
EOF

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

## 🔧 配置选项

### 环境变量

| 变量名 | 描述 | 默认值 | 必需 |
|--------|------|--------|------|
| `API_KEY` | 上游服务的API密钥 | - | ✅ |
| `UPSTREAM_URL` | 上游API服务地址 | `https://api.openai.com` | ❌ |
| `PORT` | 代理服务器监听端口 | `8000` | ❌ |

### 支持的上游服务

#### OpenAI
```bash
export UPSTREAM_URL="https://api.openai.com"
export API_KEY="sk-..."
```

#### Azure OpenAI
```bash
export UPSTREAM_URL="https://your-resource.openai.azure.com"
export API_KEY="your-azure-key"
```

#### Anthropic Claude (通过兼容层)
```bash
export UPSTREAM_URL="https://api.anthropic.com"
export API_KEY="sk-ant-..."
```

#### 本地模型服务
```bash
# Ollama
export UPSTREAM_URL="http://localhost:11434"
export API_KEY="ollama"

# LM Studio
export UPSTREAM_URL="http://localhost:1234"
export API_KEY="lm-studio"

# Text Generation WebUI
export UPSTREAM_URL="http://localhost:5000"
export API_KEY="local"
```

## 🧪 测试部署

### 基本功能测试

```bash
# 使用启动脚本测试
./start.sh test

# 或直接运行测试
deno run --allow-net example_usage.ts test http://localhost:8000
```

### 手动测试

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "What is 2+2?"}
    ],
    "tools": [
      {
        "type": "function",
        "function": {
          "name": "calculate",
          "description": "Perform mathematical calculations",
          "parameters": {
            "type": "object",
            "properties": {
              "expression": {"type": "string"}
            },
            "required": ["expression"]
          }
        }
      }
    ]
  }'
```

### 健康检查

```bash
curl http://localhost:8000/health
```

## 🔒 生产环境部署

### 1. 使用反向代理

推荐使用Nginx作为反向代理：

```bash
# 使用包含Nginx的Docker Compose配置
docker-compose --profile with-nginx up -d
```

### 2. SSL/TLS配置

编辑 `nginx.conf` 文件，取消注释HTTPS配置部分，并提供SSL证书。

### 3. 环境变量安全

生产环境中，建议使用密钥管理服务：

```bash
# 使用Docker secrets
echo "your-api-key" | docker secret create api_key -

# 或使用环境变量文件
echo "API_KEY=your-api-key" > .env.production
chmod 600 .env.production
```

### 4. 监控和日志

```bash
# 查看容器日志
docker logs -f function-call-proxy

# 使用Docker Compose查看所有服务日志
docker-compose logs -f

# 监控资源使用
docker stats function-call-proxy
```

### 5. 性能调优

#### 调整并发限制
编辑 `nginx.conf` 中的限流配置：

```nginx
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req zone=api burst=20 nodelay;
```

#### 调整超时设置
```nginx
proxy_connect_timeout 60s;
proxy_send_timeout 60s;
proxy_read_timeout 60s;
```

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查看端口使用情况
lsof -i :8000

# 更改端口
export PORT=8001
```

#### 2. API密钥无效
```bash
# 检查API密钥格式
echo $API_KEY | wc -c

# 测试上游服务连接
curl -H "Authorization: Bearer $API_KEY" $UPSTREAM_URL/v1/models
```

#### 3. 网络连接问题
```bash
# 测试网络连接
curl -I $UPSTREAM_URL

# 检查DNS解析
nslookup api.openai.com
```

#### 4. Docker相关问题
```bash
# 查看容器状态
docker ps -a

# 查看容器日志
docker logs function-call-proxy

# 重启容器
docker restart function-call-proxy
```

### 调试模式

启用详细日志：

```bash
# 本地调试
DEBUG=1 deno run --allow-net --allow-env openai_function_call_proxy.ts

# Docker调试
docker run -e DEBUG=1 -e API_KEY="$API_KEY" function-call-proxy
```

## 📊 性能基准

### 延迟对比

| 场景 | 原生Function Call | XML代理 | 增加延迟 |
|------|------------------|---------|----------|
| 简单工具调用 | ~200ms | ~250ms | +25% |
| 复杂工具调用 | ~500ms | ~600ms | +20% |
| 多工具调用 | ~800ms | ~1000ms | +25% |

### 资源使用

- **内存**: ~50MB (基础运行)
- **CPU**: 低负载 (<5% 单核)
- **网络**: 与上游服务带宽相当

## 🔄 更新和维护

### 更新代理服务

```bash
# 停止服务
docker-compose down

# 拉取最新代码
git pull

# 重新构建和启动
docker-compose up -d --build
```

### 备份配置

```bash
# 备份配置文件
tar -czf backup-$(date +%Y%m%d).tar.gz \
  .env docker-compose.yml nginx.conf
```

### 监控脚本

创建简单的监控脚本：

```bash
#!/bin/bash
# monitor.sh

while true; do
  if ! curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "$(date): Service is down, restarting..."
    docker-compose restart function-call-proxy
  fi
  sleep 30
done
```

## 📞 支持

如果遇到问题，请检查：

1. 环境变量配置是否正确
2. 网络连接是否正常
3. API密钥是否有效
4. 上游服务是否可用

更多帮助请参考项目文档或提交Issue。
