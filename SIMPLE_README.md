# 简化版 OpenAI Function Call 代理服务器

让任意模型都支持 OpenAI Function Call 功能的轻量级代理服务器。

## 🌟 特性

- **简单直接**: 只需一种URL格式 `/{upstream_url}`
- **多模态支持**: 完整支持文本、图片、音频等内容格式
- **动态路由**: 无需预配置，从URL直接解析上游服务
- **动态认证**: 从请求头提取API密钥，支持多用户
- **轻量级**: 代码简洁，易于理解和维护

## 🚀 快速开始

### 1. 启动服务器

```bash
# 可选：设置端口
export PORT=8000

# 启动服务器
deno run --allow-net --allow-env simple_function_call_proxy.ts
```

### 2. 使用代理

URL格式：`http://localhost:8000/{upstream_url}`

#### OpenAI API
```bash
curl -X POST http://localhost:8000/https://api.openai.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-openai-key" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {"role": "user", "content": "What is 15 * 23?"}
    ],
    "tools": [
      {
        "type": "function",
        "function": {
          "name": "calculate",
          "description": "Perform mathematical calculations",
          "parameters": {
            "type": "object",
            "properties": {
              "expression": {"type": "string"}
            },
            "required": ["expression"]
          }
        }
      }
    ]
  }'
```

#### Anthropic Claude
```bash
curl -X POST http://localhost:8000/https://api.anthropic.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-ant-your-claude-key" \
  -d '{
    "model": "claude-3-sonnet-20240229",
    "messages": [
      {"role": "user", "content": "Search for information about Deno"}
    ],
    "tools": [
      {
        "type": "function",
        "function": {
          "name": "web_search",
          "description": "Search the web for information",
          "parameters": {
            "type": "object",
            "properties": {
              "query": {"type": "string"}
            },
            "required": ["query"]
          }
        }
      }
    ]
  }'
```

#### 本地服务（Ollama）
```bash
curl -X POST http://localhost:8000/http://localhost:11434/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ollama" \
  -d '{
    "model": "llama3.1:8b",
    "messages": [
      {"role": "user", "content": "Generate a random number"}
    ],
    "tools": [
      {
        "type": "function",
        "function": {
          "name": "random_number",
          "description": "Generate a random number",
          "parameters": {
            "type": "object",
            "properties": {
              "min": {"type": "number"},
              "max": {"type": "number"}
            },
            "required": ["min", "max"]
          }
        }
      }
    ]
  }'
```

## 🔧 工作原理

1. **URL解析**: 从请求URL中提取上游服务地址
2. **认证提取**: 从Authorization头提取API密钥
3. **消息转换**: 将tools转换为XML格式的System Prompt
4. **请求代理**: 转发请求到上游服务
5. **响应解析**: 解析XML格式的工具调用，转换为标准格式

### XML格式示例

模型需要按以下格式生成工具调用：

```xml
<tool_use>
  <name>function_name</name>
  <arguments>{"param1": "value1", "param2": "value2"}</arguments>
</tool_use>
```

## 🧪 测试

```bash
# 运行所有测试
deno run --allow-net test_simple_proxy.ts

# 健康检查
deno run --allow-net test_simple_proxy.ts health

# API信息
deno run --allow-net test_simple_proxy.ts info

# URL解析测试
deno run --allow-net test_simple_proxy.ts url
```

## 📋 支持的内容格式

### 文本消息
```json
{
  "role": "user",
  "content": "Hello, world!"
}
```

### 多模态消息
```json
{
  "role": "user",
  "content": [
    {"type": "text", "text": "What's in this image?"},
    {
      "type": "image_url",
      "image_url": {
        "url": "data:image/jpeg;base64,/9j/4AAQ...",
        "detail": "auto"
      }
    }
  ]
}
```

### 音频消息
```json
{
  "role": "user",
  "content": [
    {"type": "text", "text": "Transcribe this audio"},
    {
      "type": "input_audio",
      "input_audio": {
        "data": "base64-encoded-audio",
        "format": "wav"
      }
    }
  ]
}
```

## 🔍 API端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/health` | GET | 健康检查 |
| `/` | GET | API信息和使用说明 |
| `/{upstream_url}` | POST | 代理聊天完成请求 |

## ⚠️ 注意事项

1. **URL编码**: 上游URL中的特殊字符需要正确编码
2. **HTTPS推荐**: 生产环境建议使用HTTPS保护API密钥
3. **模型兼容性**: 不同模型对XML格式的理解能力可能不同
4. **错误处理**: 上游服务的错误会直接返回给客户端

## 🐳 Docker部署

```dockerfile
FROM denoland/deno:1.40.2
WORKDIR /app
COPY simple_function_call_proxy.ts .
RUN deno cache simple_function_call_proxy.ts
EXPOSE 8000
CMD ["deno", "run", "--allow-net", "--allow-env", "simple_function_call_proxy.ts"]
```

```bash
# 构建镜像
docker build -t simple-function-call-proxy .

# 运行容器
docker run -d -p 8000:8000 simple-function-call-proxy
```

## 📊 性能特点

- **内存占用**: ~30MB
- **启动时间**: <1秒
- **延迟开销**: +20-50ms（XML解析）
- **并发支持**: 基于Deno的异步处理

## 🔒 安全建议

1. **API密钥保护**: 使用HTTPS传输
2. **访问控制**: 在生产环境中添加认证层
3. **日志脱敏**: 避免记录完整的API密钥
4. **网络隔离**: 限制代理服务的网络访问范围

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

---

**简单、直接、有效** - 这就是我们的设计理念。
