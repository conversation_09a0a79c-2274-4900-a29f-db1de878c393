#!/bin/bash

# OpenAI Function Call Proxy 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查依赖
check_dependencies() {
    print_message $BLUE "🔍 检查依赖..."
    
    if ! command -v deno &> /dev/null; then
        print_message $RED "❌ Deno 未安装，请先安装 Deno"
        echo "安装命令: curl -fsSL https://deno.land/x/install/install.sh | sh"
        exit 1
    fi
    
    print_message $GREEN "✅ Deno 已安装: $(deno --version | head -n1)"
}

# 检查环境变量
check_env() {
    print_message $BLUE "🔍 检查环境变量..."
    
    if [ -z "$API_KEY" ]; then
        print_message $YELLOW "⚠️  API_KEY 环境变量未设置"
        read -p "请输入 API Key: " API_KEY
        export API_KEY
    fi
    
    if [ -z "$UPSTREAM_URL" ]; then
        export UPSTREAM_URL="https://api.openai.com"
        print_message $YELLOW "⚠️  使用默认上游地址: $UPSTREAM_URL"
    fi
    
    if [ -z "$PORT" ]; then
        export PORT="8000"
        print_message $YELLOW "⚠️  使用默认端口: $PORT"
    fi
    
    print_message $GREEN "✅ 环境变量配置完成"
    echo "   - UPSTREAM_URL: $UPSTREAM_URL"
    echo "   - PORT: $PORT"
    echo "   - API_KEY: ${API_KEY:0:10}..."
}

# 启动服务器
start_server() {
    print_message $BLUE "🚀 启动 Function Call Proxy 服务器..."
    print_message $GREEN "📡 服务器地址: http://localhost:$PORT"
    print_message $GREEN "📖 API文档: http://localhost:$PORT/v1/chat/completions"
    print_message $YELLOW "💡 按 Ctrl+C 停止服务器"
    echo ""
    
    deno run --allow-net --allow-env openai_function_call_proxy.ts
}

# 运行测试
run_tests() {
    print_message $BLUE "🧪 运行测试..."
    
    # 启动服务器（后台）
    deno run --allow-net --allow-env openai_function_call_proxy.ts &
    SERVER_PID=$!
    
    # 等待服务器启动
    sleep 3
    
    # 运行测试
    deno run --allow-net example_usage.ts test
    
    # 停止服务器
    kill $SERVER_PID
    
    print_message $GREEN "✅ 测试完成"
}

# Docker 部署
deploy_docker() {
    print_message $BLUE "🐳 使用 Docker 部署..."
    
    if ! command -v docker &> /dev/null; then
        print_message $RED "❌ Docker 未安装"
        exit 1
    fi
    
    # 构建镜像
    print_message $BLUE "📦 构建 Docker 镜像..."
    docker build -t function-call-proxy .
    
    # 运行容器
    print_message $BLUE "🚀 启动 Docker 容器..."
    docker run -d \
        --name function-call-proxy \
        -p 8000:8000 \
        -e API_KEY="$API_KEY" \
        -e UPSTREAM_URL="$UPSTREAM_URL" \
        function-call-proxy
    
    print_message $GREEN "✅ Docker 容器已启动"
    print_message $GREEN "📡 服务器地址: http://localhost:8000"
    
    # 显示日志
    print_message $BLUE "📋 容器日志:"
    docker logs -f function-call-proxy
}

# Docker Compose 部署
deploy_compose() {
    print_message $BLUE "🐳 使用 Docker Compose 部署..."
    
    if ! command -v docker-compose &> /dev/null; then
        print_message $RED "❌ Docker Compose 未安装"
        exit 1
    fi
    
    # 创建 .env 文件
    cat > .env << EOF
API_KEY=$API_KEY
UPSTREAM_URL=$UPSTREAM_URL
EOF
    
    # 启动服务
    docker-compose up -d
    
    print_message $GREEN "✅ Docker Compose 服务已启动"
    print_message $GREEN "📡 服务器地址: http://localhost:8000"
    
    # 显示日志
    print_message $BLUE "📋 服务日志:"
    docker-compose logs -f
}

# 显示帮助
show_help() {
    echo "OpenAI Function Call Proxy 启动脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     - 启动代理服务器 (默认)"
    echo "  test      - 运行测试"
    echo "  docker    - 使用 Docker 部署"
    echo "  compose   - 使用 Docker Compose 部署"
    echo "  help      - 显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  API_KEY      - API 密钥 (必需)"
    echo "  UPSTREAM_URL - 上游服务地址 (默认: https://api.openai.com)"
    echo "  PORT         - 服务器端口 (默认: 8000)"
    echo ""
    echo "示例:"
    echo "  export API_KEY='your-api-key'"
    echo "  export UPSTREAM_URL='https://api.openai.com'"
    echo "  $0 start"
    echo ""
    echo "  $0 test"
    echo "  $0 docker"
}

# 主函数
main() {
    local command=${1:-start}
    
    case $command in
        start)
            check_dependencies
            check_env
            start_server
            ;;
        test)
            check_dependencies
            check_env
            run_tests
            ;;
        docker)
            check_env
            deploy_docker
            ;;
        compose)
            check_env
            deploy_compose
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "❌ 未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap 'print_message $YELLOW "\n👋 服务器已停止"; exit 0' INT

# 运行主函数
main "$@"
