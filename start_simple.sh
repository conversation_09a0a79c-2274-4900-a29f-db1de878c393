#!/bin/bash

# 简化版 Function Call Proxy 启动脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查Deno
check_deno() {
    if ! command -v deno &> /dev/null; then
        print_message $YELLOW "⚠️  Deno 未安装，正在安装..."
        curl -fsSL https://deno.land/x/install/install.sh | sh
        export PATH="$HOME/.deno/bin:$PATH"
    fi
    print_message $GREEN "✅ Deno 已安装: $(deno --version | head -n1)"
}

# 启动服务器
start_server() {
    local port=${PORT:-8000}
    
    print_message $BLUE "🚀 启动简化版 Function Call Proxy..."
    print_message $GREEN "📡 服务器地址: http://localhost:$port"
    print_message $GREEN "📖 URL格式: /{upstream_url}"
    print_message $GREEN "📝 示例: http://localhost:$port/https://api.openai.com/v1/chat/completions"
    print_message $YELLOW "💡 按 Ctrl+C 停止服务器"
    echo ""
    
    deno run --allow-net --allow-env simple_function_call_proxy.ts
}

# 运行测试
run_tests() {
    print_message $BLUE "🧪 运行测试..."
    
    # 启动服务器（后台）
    deno run --allow-net --allow-env simple_function_call_proxy.ts &
    SERVER_PID=$!
    
    # 等待服务器启动
    sleep 3
    
    # 运行测试
    deno run --allow-net test_simple_proxy.ts
    
    # 停止服务器
    kill $SERVER_PID
    
    print_message $GREEN "✅ 测试完成"
}

# 显示帮助
show_help() {
    echo "简化版 Function Call Proxy 启动脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start   - 启动代理服务器 (默认)"
    echo "  test    - 运行测试"
    echo "  help    - 显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  PORT    - 服务器端口 (默认: 8000)"
    echo ""
    echo "示例:"
    echo "  $0 start"
    echo "  PORT=3000 $0 start"
    echo "  $0 test"
}

# 主函数
main() {
    local command=${1:-start}
    
    case $command in
        start)
            check_deno
            start_server
            ;;
        test)
            check_deno
            run_tests
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $YELLOW "❌ 未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap 'print_message $YELLOW "\n👋 服务器已停止"; exit 0' INT

# 运行主函数
main "$@"
