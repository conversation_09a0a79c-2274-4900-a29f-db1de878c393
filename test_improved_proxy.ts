#!/usr/bin/env deno run --allow-net

/**
 * 测试改进后的Function Call代理服务器
 * 验证新的URL格式、动态路由和多内容类型支持
 */

interface TestCase {
  name: string;
  url: string;
  headers: Record<string, string>;
  body: any;
  expectedFeatures: string[];
}

const testCases: TestCase[] = [
  {
    name: "OpenAI服务商简写格式",
    url: "http://localhost:8000/proxy/openai/v1/chat/completions",
    headers: {
      "Content-Type": "application/json",
      "Authorization": "Bearer sk-test-key"
    },
    body: {
      model: "gpt-4",
      messages: [
        { role: "user", content: "What's 2+2?" }
      ],
      tools: [
        {
          type: "function",
          function: {
            name: "calculate",
            description: "Perform calculations",
            parameters: {
              type: "object",
              properties: {
                expression: { type: "string" }
              },
              required: ["expression"]
            }
          }
        }
      ]
    },
    expectedFeatures: ["dynamic_routing", "xml_parsing"]
  },
  {
    name: "简化路径格式",
    url: "http://localhost:8000/anthropic/v1/chat/completions",
    headers: {
      "Content-Type": "application/json",
      "Authorization": "Bearer sk-ant-test-key"
    },
    body: {
      model: "claude-3-sonnet",
      messages: [
        { role: "user", content: "Search for Deno information" }
      ],
      tools: [
        {
          type: "function",
          function: {
            name: "web_search",
            description: "Search the web",
            parameters: {
              type: "object",
              properties: {
                query: { type: "string" }
              },
              required: ["query"]
            }
          }
        }
      ]
    },
    expectedFeatures: ["dynamic_routing", "xml_parsing"]
  },
  {
    name: "查询参数格式",
    url: "http://localhost:8000/v1/chat/completions?upstream=https://api.openai.com",
    headers: {
      "Content-Type": "application/json",
      "Authorization": "Bearer sk-test-key"
    },
    body: {
      model: "gpt-3.5-turbo",
      messages: [
        { role: "user", content: "Generate an image of a cat" }
      ],
      tools: [
        {
          type: "function",
          function: {
            name: "generate_image",
            description: "Generate an image",
            parameters: {
              type: "object",
              properties: {
                prompt: { type: "string" },
                size: { type: "string", enum: ["256x256", "512x512", "1024x1024"] }
              },
              required: ["prompt"]
            }
          }
        }
      ]
    },
    expectedFeatures: ["query_param_routing", "xml_parsing"]
  },
  {
    name: "多模态内容支持",
    url: "http://localhost:8000/proxy/openai/v1/chat/completions",
    headers: {
      "Content-Type": "application/json",
      "Authorization": "Bearer sk-test-key"
    },
    body: {
      model: "gpt-4-vision-preview",
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: "What's in this image?" },
            {
              type: "image_url",
              image_url: {
                url: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=",
                detail: "auto"
              }
            }
          ]
        }
      ],
      tools: [
        {
          type: "function",
          function: {
            name: "analyze_image",
            description: "Analyze image content",
            parameters: {
              type: "object",
              properties: {
                description: { type: "string" }
              },
              required: ["description"]
            }
          }
        }
      ]
    },
    expectedFeatures: ["multimodal_content", "xml_parsing"]
  }
];

async function testImprovedProxy() {
  console.log("🧪 测试改进后的Function Call代理服务器");
  console.log("=" .repeat(60));

  // 首先测试健康检查
  console.log("\n🏥 健康检查测试");
  console.log("-".repeat(40));
  
  try {
    const healthResponse = await fetch("http://localhost:8000/health");
    const healthData = await healthResponse.json();
    
    console.log("✅ 健康检查通过");
    console.log(`   状态: ${healthData.status}`);
    console.log(`   版本: ${healthData.version}`);
    console.log(`   功能: ${healthData.features.join(', ')}`);
  } catch (error) {
    console.error("❌ 健康检查失败:", error.message);
    return;
  }

  // 测试API信息端点
  console.log("\n📖 API信息测试");
  console.log("-".repeat(40));
  
  try {
    const infoResponse = await fetch("http://localhost:8000/");
    const infoData = await infoResponse.json();
    
    console.log("✅ API信息获取成功");
    console.log(`   名称: ${infoData.name}`);
    console.log(`   描述: ${infoData.description}`);
    console.log(`   支持的服务: ${infoData.supported_services.join(', ')}`);
  } catch (error) {
    console.error("❌ API信息获取失败:", error.message);
  }

  // 测试各种URL格式
  for (const testCase of testCases) {
    console.log(`\n🔍 测试: ${testCase.name}`);
    console.log("-".repeat(40));
    console.log(`📡 URL: ${testCase.url}`);

    try {
      const response = await fetch(testCase.url, {
        method: "POST",
        headers: testCase.headers,
        body: JSON.stringify(testCase.body)
      });

      console.log(`📊 状态码: ${response.status}`);
      
      if (response.ok) {
        const result = await response.json();
        
        // 检查响应结构
        if (result.choices && result.choices[0]) {
          const message = result.choices[0].message;
          console.log(`✅ 响应格式正确`);
          console.log(`   角色: ${message.role}`);
          
          if (message.content) {
            const contentPreview = typeof message.content === 'string' 
              ? message.content.substring(0, 100)
              : JSON.stringify(message.content).substring(0, 100);
            console.log(`   内容预览: ${contentPreview}...`);
          }
          
          if (message.tool_calls) {
            console.log(`   工具调用数量: ${message.tool_calls.length}`);
            message.tool_calls.forEach((tc: any, i: number) => {
              console.log(`     ${i + 1}. ${tc.function.name}`);
            });
          }
        } else {
          console.log("⚠️  响应格式异常");
          console.log(`   响应: ${JSON.stringify(result).substring(0, 200)}...`);
        }
      } else {
        const errorText = await response.text();
        console.log(`❌ 请求失败: ${response.status}`);
        console.log(`   错误: ${errorText.substring(0, 200)}...`);
      }

    } catch (error) {
      console.error(`❌ 测试失败: ${error.message}`);
    }
  }

  console.log("\n" + "=".repeat(60));
  console.log("🎉 测试完成!");
}

// URL解析测试
async function testUrlParsing() {
  console.log("\n🔗 URL解析测试");
  console.log("-".repeat(40));

  const urlTests = [
    {
      input: "http://localhost:8000/proxy/openai/v1/chat/completions",
      expected: "https://api.openai.com"
    },
    {
      input: "http://localhost:8000/anthropic/v1/chat/completions",
      expected: "https://api.anthropic.com"
    },
    {
      input: "http://localhost:8000/v1/chat/completions?upstream=https://custom.api.com",
      expected: "https://custom.api.com"
    },
    {
      input: "http://localhost:8000/ollama/v1/chat/completions",
      expected: "http://localhost:11434"
    }
  ];

  for (const test of urlTests) {
    console.log(`🔍 测试URL: ${test.input}`);
    console.log(`   期望上游: ${test.expected}`);
    
    // 这里我们通过发送请求并观察错误信息来验证URL解析
    // 因为我们没有真实的API密钥，所以会收到认证错误，但可以从错误中看出上游URL是否正确
    try {
      const response = await fetch(test.input, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer invalid-key"
        },
        body: JSON.stringify({
          model: "test",
          messages: [{ role: "user", content: "test" }]
        })
      });
      
      console.log(`   状态码: ${response.status}`);
      if (!response.ok) {
        const errorText = await response.text();
        console.log(`   响应: ${errorText.substring(0, 100)}...`);
      }
    } catch (error) {
      console.log(`   网络错误: ${error.message}`);
    }
  }
}

// 主函数
async function main() {
  const args = Deno.args;
  const command = args[0] || "all";

  switch (command) {
    case "all":
      await testImprovedProxy();
      await testUrlParsing();
      break;
    case "health":
      const response = await fetch("http://localhost:8000/health");
      console.log(await response.json());
      break;
    case "info":
      const infoResponse = await fetch("http://localhost:8000/");
      console.log(await infoResponse.json());
      break;
    case "url":
      await testUrlParsing();
      break;
    case "help":
      console.log(`
使用方法:
  deno run --allow-net test_improved_proxy.ts [command]

命令:
  all     - 运行所有测试 (默认)
  health  - 健康检查
  info    - API信息
  url     - URL解析测试
  help    - 显示帮助
      `);
      break;
    default:
      console.error(`未知命令: ${command}`);
      console.log("使用 'help' 查看可用命令");
  }
}

if (import.meta.main) {
  main();
}
