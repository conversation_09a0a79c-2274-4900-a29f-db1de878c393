#!/usr/bin/env deno run --allow-net

/**
 * 测试对话流程，验证历史上下文处理
 */

async function testConversationFlow() {
  console.log("🧪 测试对话流程和历史上下文处理");
  console.log("=" .repeat(50));

  const baseUrl = "http://localhost:8000/https://api.openai.com/v1/chat/completions";
  const apiKey = "sk-test-key"; // 测试用的假密钥

  // 模拟一个完整的对话流程
  const conversation = [
    {
      name: "第一轮：用户请求计算",
      messages: [
        { role: "user", content: "What is 15 * 23? Please use a calculator." }
      ],
      tools: [
        {
          type: "function",
          function: {
            name: "calculate",
            description: "Perform mathematical calculations",
            parameters: {
              type: "object",
              properties: {
                expression: { type: "string", description: "Mathematical expression" }
              },
              required: ["expression"]
            }
          }
        }
      ]
    },
    {
      name: "第二轮：模拟助手响应和工具调用",
      messages: [
        { role: "user", content: "What is 15 * 23? Please use a calculator." },
        {
          role: "assistant",
          content: "I'll calculate 15 * 23 for you.",
          tool_calls: [
            {
              id: "call_123",
              type: "function",
              function: {
                name: "calculate",
                arguments: '{"expression": "15 * 23"}'
              }
            }
          ]
        },
        {
          role: "tool",
          tool_call_id: "call_123",
          name: "calculate",
          content: "345"
        }
      ],
      tools: [
        {
          type: "function",
          function: {
            name: "calculate",
            description: "Perform mathematical calculations",
            parameters: {
              type: "object",
              properties: {
                expression: { type: "string", description: "Mathematical expression" }
              },
              required: ["expression"]
            }
          }
        }
      ]
    },
    {
      name: "第三轮：继续对话",
      messages: [
        { role: "user", content: "What is 15 * 23? Please use a calculator." },
        {
          role: "assistant",
          content: "I'll calculate 15 * 23 for you.",
          tool_calls: [
            {
              id: "call_123",
              type: "function",
              function: {
                name: "calculate",
                arguments: '{"expression": "15 * 23"}'
              }
            }
          ]
        },
        {
          role: "tool",
          tool_call_id: "call_123",
          name: "calculate",
          content: "345"
        },
        { role: "assistant", content: "The result of 15 * 23 is 345." },
        { role: "user", content: "Now calculate the square root of that result." }
      ],
      tools: [
        {
          type: "function",
          function: {
            name: "calculate",
            description: "Perform mathematical calculations",
            parameters: {
              type: "object",
              properties: {
                expression: { type: "string", description: "Mathematical expression" }
              },
              required: ["expression"]
            }
          }
        }
      ]
    }
  ];

  for (let i = 0; i < conversation.length; i++) {
    const round = conversation[i];
    console.log(`\n🔄 ${round.name}`);
    console.log("-".repeat(40));
    console.log(`📝 消息数量: ${round.messages.length}`);
    
    // 显示消息内容概览
    round.messages.forEach((msg, idx) => {
      const contentPreview = typeof msg.content === 'string' 
        ? msg.content.substring(0, 50) 
        : JSON.stringify(msg.content).substring(0, 50);
      console.log(`   ${idx + 1}. [${msg.role}] ${contentPreview}${contentPreview.length >= 50 ? '...' : ''}`);
      if (msg.tool_calls) {
        console.log(`      工具调用: ${msg.tool_calls.map(tc => tc.function.name).join(', ')}`);
      }
    });

    try {
      const response = await fetch(baseUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: "gpt-3.5-turbo",
          messages: round.messages,
          tools: round.tools,
          temperature: 0.7,
          max_tokens: 1000
        })
      });

      console.log(`📊 状态码: ${response.status}`);
      
      if (response.ok) {
        const result = await response.json();
        
        if (result.choices && result.choices[0]) {
          const message = result.choices[0].message;
          console.log(`✅ 响应成功`);
          console.log(`   角色: ${message.role}`);
          
          if (message.content) {
            console.log(`   内容长度: ${message.content.length} 字符`);
            console.log(`   内容预览: ${message.content.substring(0, 100)}...`);
          } else {
            console.log(`   内容: null`);
          }
          
          if (message.tool_calls) {
            console.log(`   工具调用: ${message.tool_calls.length}个`);
            message.tool_calls.forEach((tc: any, idx: number) => {
              console.log(`     ${idx + 1}. ${tc.function.name}(${tc.function.arguments})`);
            });
          } else {
            console.log(`   工具调用: 无`);
          }
        } else {
          console.log(`⚠️  响应格式异常`);
          console.log(`   完整响应: ${JSON.stringify(result).substring(0, 200)}...`);
        }
      } else {
        const errorText = await response.text();
        console.log(`❌ 请求失败`);
        console.log(`   错误: ${errorText.substring(0, 200)}...`);
      }

    } catch (error) {
      console.error(`❌ 测试失败: ${error.message}`);
    }

    // 等待一下再进行下一轮测试
    if (i < conversation.length - 1) {
      console.log("⏳ 等待 2 秒...");
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  console.log("\n" + "=".repeat(50));
  console.log("🎉 对话流程测试完成!");
}

// 测试XML解析
async function testXmlParsing() {
  console.log("\n🔍 测试XML解析功能");
  console.log("-".repeat(30));

  const testCases = [
    {
      name: "标准XML格式",
      content: `I'll help you calculate that.

<tool_use>
  <name>calculate</name>
  <arguments>{"expression": "15 * 23"}</arguments>
</tool_use>

Let me get the result for you.`
    },
    {
      name: "多个工具调用",
      content: `I'll calculate and then convert to binary.

<tool_use>
  <name>calculate</name>
  <arguments>{"expression": "15 * 23"}</arguments>
</tool_use>

<tool_use>
  <name>convert_base</name>
  <arguments>{"number": 345, "from_base": 10, "to_base": 2}</arguments>
</tool_use>

Here are the results.`
    },
    {
      name: "带额外空白的XML",
      content: `Let me calculate this.

<tool_use>
  <name>  calculate  </name>
  <arguments>  {"expression": "sqrt(345)"}  </arguments>
</tool_use>

The calculation is complete.`
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📝 测试: ${testCase.name}`);
    
    // 模拟XML解析（简化版本）
    const toolUsePattern = /<tool_use>\s*<name>(.*?)<\/name>\s*<arguments>(.*?)<\/arguments>\s*<\/tool_use>/gs;
    const matches = [];
    let match;

    while ((match = toolUsePattern.exec(testCase.content)) !== null) {
      matches.push({
        toolName: match[1].trim(),
        arguments: match[2].trim(),
        fullMatch: match[0]
      });
    }

    console.log(`   找到工具调用: ${matches.length}个`);
    matches.forEach((m, i) => {
      console.log(`     ${i + 1}. ${m.toolName}(${m.arguments})`);
    });
  }
}

// 主函数
async function main() {
  const args = Deno.args;
  const command = args[0] || "all";

  switch (command) {
    case "all":
      await testConversationFlow();
      await testXmlParsing();
      break;
    case "conversation":
      await testConversationFlow();
      break;
    case "xml":
      await testXmlParsing();
      break;
    case "help":
      console.log(`
使用方法:
  deno run --allow-net test_conversation_flow.ts [command]

命令:
  all          - 运行所有测试 (默认)
  conversation - 测试对话流程
  xml          - 测试XML解析
  help         - 显示帮助
      `);
      break;
    default:
      console.error(`未知命令: ${command}`);
      console.log("使用 'help' 查看可用命令");
  }
}

if (import.meta.main) {
  main();
}
