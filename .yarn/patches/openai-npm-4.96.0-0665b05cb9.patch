diff --git a/core.js b/core.js
index 862d66101f441fb4f47dfc8cff5e2d39e1f5a11e..6464bebbf696c39d35f0368f061ea4236225c162 100644
--- a/core.js
+++ b/core.js
@@ -159,7 +159,7 @@ class APIClient {
             Accept: 'application/json',
             'Content-Type': 'application/json',
             'User-Agent': this.getUserAgent(),
-            ...getPlatformHeaders(),
+            // ...getPlatformHeaders(),
             ...this.authHeaders(opts),
         };
     }
diff --git a/core.mjs b/core.mjs
index 05dbc6cfde51589a2b100d4e4b5b3c1a33b32b89..789fbb4985eb952a0349b779fa83b1a068af6e7e 100644
--- a/core.mjs
+++ b/core.mjs
@@ -152,7 +152,7 @@ export class APIClient {
             Accept: 'application/json',
             'Content-Type': 'application/json',
             'User-Agent': this.getUserAgent(),
-            ...getPlatformHeaders(),
+            // ...getPlatformHeaders(),
             ...this.authHeaders(opts),
         };
     }
diff --git a/error.mjs b/error.mjs
index 7d19f5578040afa004bc887aab1725e8703d2bac..59ec725b6142299a62798ac4bdedb63ba7d9932c 100644
--- a/error.mjs
+++ b/error.mjs
@@ -36,7 +36,7 @@ export class APIError extends OpenAIError {
         if (!status || !headers) {
             return new APIConnectionError({ message, cause: castToError(errorResponse) });
         }
-        const error = errorResponse?.['error'];
+        const error = errorResponse?.['error'] || errorResponse;
         if (status === 400) {
             return new BadRequestError(status, error, message, headers);
         }
diff --git a/resources/embeddings.js b/resources/embeddings.js
index aae578404cb2d09a39ac33fc416f1c215c45eecd..25c54b05bdae64d5c3b36fbb30dc7c8221b14034 100644
--- a/resources/embeddings.js
+++ b/resources/embeddings.js
@@ -36,6 +36,9 @@ class Embeddings extends resource_1.APIResource {
         // No encoding_format specified, defaulting to base64 for performance reasons
         // See https://github.com/openai/openai-node/pull/1312
         let encoding_format = hasUserProvidedEncodingFormat ? body.encoding_format : 'base64';
+        if (body.model.includes('jina')) {
+            encoding_format = undefined;
+        }
         if (hasUserProvidedEncodingFormat) {
             Core.debug('Request', 'User defined encoding_format:', body.encoding_format);
         }
@@ -47,7 +50,7 @@ class Embeddings extends resource_1.APIResource {
             ...options,
         });
         // if the user specified an encoding_format, return the response as-is
-        if (hasUserProvidedEncodingFormat) {
+        if (hasUserProvidedEncodingFormat || body.model.includes('jina')) {
             return response;
         }
         // in this stage, we are sure the user did not specify an encoding_format
diff --git a/resources/embeddings.mjs b/resources/embeddings.mjs
index 0df3c6cc79a520e54acb4c2b5f77c43b774035ff..aa488b8a11b2c413c0a663d9a6059d286d7b5faf 100644
--- a/resources/embeddings.mjs
+++ b/resources/embeddings.mjs
@@ -10,6 +10,9 @@ export class Embeddings extends APIResource {
         // No encoding_format specified, defaulting to base64 for performance reasons
         // See https://github.com/openai/openai-node/pull/1312
         let encoding_format = hasUserProvidedEncodingFormat ? body.encoding_format : 'base64';
+        if (body.model.includes('jina')) {
+            encoding_format = undefined;
+        }
         if (hasUserProvidedEncodingFormat) {
             Core.debug('Request', 'User defined encoding_format:', body.encoding_format);
         }
@@ -21,7 +24,7 @@ export class Embeddings extends APIResource {
             ...options,
         });
         // if the user specified an encoding_format, return the response as-is
-        if (hasUserProvidedEncodingFormat) {
+        if (hasUserProvidedEncodingFormat || body.model.includes('jina')) {
             return response;
         }
         // in this stage, we are sure the user did not specify an encoding_format
