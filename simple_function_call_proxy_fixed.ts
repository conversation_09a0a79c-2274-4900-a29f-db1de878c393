#!/usr/bin/env deno run --allow-net --allow-env

/**
 * 简化版 OpenAI Function Call Proxy Server (修复版)
 * 支持格式: http://localhost:8000/{upstream_url}
 * 例如: http://localhost:8000/https://api.openai.com/v1/chat/completions
 */

import { serve } from "https://deno.land/std@0.208.0/http/server.ts";

interface OpenAIMessage {
  role: "system" | "user" | "assistant" | "tool";
  content: string | null | OpenAIMessageContent[];
  tool_calls?: ToolCall[];
  tool_call_id?: string;
  name?: string;
}

interface OpenAIMessageContent {
  type: "text" | "image_url" | "input_audio";
  text?: string;
  image_url?: {
    url: string;
    detail?: "auto" | "low" | "high";
  };
  input_audio?: {
    data: string;
    format: "wav" | "mp3";
  };
}

interface ToolCall {
  id: string;
  type: "function";
  function: {
    name: string;
    arguments: string;
  };
}

interface FunctionDefinition {
  name: string;
  description: string;
  parameters: Record<string, any>;
}

interface ChatCompletionRequest {
  model: string;
  messages: OpenAIMessage[];
  tools?: { type: "function"; function: FunctionDefinition }[];
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
}

interface ToolUseMatch {
  toolName: string;
  arguments: string;
  fullMatch: string;
}

class SimpleFunctionCallProxy {
  /**
   * 从请求URL解析上游服务地址
   */
  private parseUpstreamUrl(requestUrl: string): string {
    const url = new URL(requestUrl);
    const pathname = url.pathname;

    // 移除开头的斜杠，获取上游URL
    const upstreamUrl = pathname.substring(1);

    // 验证是否是有效的URL
    if (upstreamUrl.startsWith('http://') || upstreamUrl.startsWith('https://')) {
      // 移除末尾的路径部分，只保留基础URL
      const upstreamBase = upstreamUrl.replace(/\/v1\/(chat\/completions|models)$/, '');
      return upstreamBase;
    }

    throw new Error('Invalid upstream URL format. Expected: /{upstream_url}');
  }

  /**
   * 从请求头获取API密钥
   */
  private extractApiKey(headers: Headers): string {
    const authHeader = headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Missing Authorization header');
    }

    if (authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    throw new Error('Invalid Authorization header format. Expected: Bearer <token>');
  }

  /**
   * 生成System Prompt，包含工具使用说明
   */
  private generateSystemPrompt(tools: FunctionDefinition[]): string {
    const toolsXml = tools.map(tool => `
<tool>
  <name>${tool.name}</name>
  <description>${tool.description}</description>
  <parameters>${JSON.stringify(tool.parameters)}</parameters>
</tool>`).join('\n');

    return `You have access to a set of tools that you can use to help answer the user's question. When you need to use a tool, format your response using XML-style tags as follows:

<tool_use>
  <name>{tool_name}</name>
  <arguments>{json_arguments}</arguments>
</tool_use>

## Available Tools
<tools>
${toolsXml}
</tools>

## Tool Use Rules
1. Use the exact tool name as specified in the available tools list
2. Provide arguments as a valid JSON object
3. Only use tools when necessary to answer the user's question
4. You can use multiple tools in sequence if needed
5. Always use the XML format exactly as shown above

If you don't need to use any tools, just respond normally without the XML tags.`;
  }

  /**
   * 解析响应中的工具调用
   */
  private parseToolUse(content: string): ToolUseMatch[] {
    const toolUsePattern = /<tool_use>\s*<name>(.*?)<\/name>\s*<arguments>(.*?)<\/arguments>\s*<\/tool_use>/gs;
    const matches: ToolUseMatch[] = [];
    let match: RegExpExecArray | null;

    while ((match = toolUsePattern.exec(content)) !== null) {
      matches.push({
        toolName: match[1].trim(),
        arguments: match[2].trim(),
        fullMatch: match[0]
      });
    }

    return matches;
  }

  /**
   * 生成工具调用ID
   */
  private generateToolCallId(): string {
    return `call_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 获取消息的文本内容
   */
  private getMessageTextContent(content: string | null | OpenAIMessageContent[]): string {
    if (typeof content === 'string') {
      return content || '';
    }
    if (Array.isArray(content)) {
      return content
        .filter(item => item.type === 'text')
        .map(item => item.text || '')
        .join('\n');
    }
    return '';
  }

  /**
   * 转换消息格式，处理工具调用
   */
  private transformMessages(messages: OpenAIMessage[], tools?: { type: "function"; function: FunctionDefinition }[]): OpenAIMessage[] {
    const transformedMessages: OpenAIMessage[] = [];
    
    // 如果有工具定义，添加system prompt
    if (tools && tools.length > 0) {
      const functionDefs = tools.map(t => t.function);
      const systemPrompt = this.generateSystemPrompt(functionDefs);
      
      // 查找现有的system消息
      const existingSystemIndex = messages.findIndex(m => m.role === "system");
      if (existingSystemIndex >= 0) {
        const existingContent = this.getMessageTextContent(messages[existingSystemIndex].content);
        // 合并system消息
        transformedMessages.push({
          role: "system",
          content: `${existingContent}\n\n${systemPrompt}`
        });
        // 跳过原始system消息
        messages = messages.filter((_, i) => i !== existingSystemIndex);
      } else {
        // 添加新的system消息
        transformedMessages.push({
          role: "system",
          content: systemPrompt
        });
      }
    }

    // 处理其他消息
    for (const message of messages) {
      if (message.role === "tool") {
        // 将tool消息转换为user消息
        const toolContent = this.getMessageTextContent(message.content);
        transformedMessages.push({
          role: "user",
          content: `<tool_result>
  <name>${message.name}</name>
  <result>${toolContent}</result>
</tool_result>`
        });
      } else if (message.role === "assistant" && message.tool_calls) {
        // 将tool_calls转换为XML格式
        let content = this.getMessageTextContent(message.content);
        for (const toolCall of message.tool_calls) {
          content += `\n\n<tool_use>
  <name>${toolCall.function.name}</name>
  <arguments>${toolCall.function.arguments}</arguments>
</tool_use>`;
        }
        transformedMessages.push({
          role: "assistant",
          content: content.trim()
        });
      } else {
        transformedMessages.push(message);
      }
    }

    return transformedMessages;
  }

  /**
   * 处理聊天完成请求
   */
  async handleChatCompletion(
    request: ChatCompletionRequest, 
    requestUrl: string, 
    requestHeaders: Headers
  ): Promise<Response> {
    try {
      // 从请求中解析上游URL和API密钥
      const upstreamUrl = this.parseUpstreamUrl(requestUrl);
      const apiKey = this.extractApiKey(requestHeaders);
      
      // 转换消息格式
      const transformedMessages = this.transformMessages(request.messages, request.tools);
      
      // 构建上游请求
      const upstreamRequest = {
        ...request,
        messages: transformedMessages,
        tools: undefined, // 移除tools参数，因为我们用system prompt代替
      };

      console.log('🔄 Proxying request to:', upstreamUrl);
      // 发送请求到上游服务
      console.log('📤 Sending request to upstream...');
      const upstreamResponse = await fetch(`${upstreamUrl}/v1/chat/completions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
        },
        body: JSON.stringify(upstreamRequest),
      });

      console.log('📥 Received response from upstream:', upstreamResponse.status);

      if (!upstreamResponse.ok) {
        console.error('❌ Upstream error:', upstreamResponse.status);
        const errorText = await upstreamResponse.text();
        console.error('❌ Error details:', errorText);
        return new Response(errorText, {
          status: upstreamResponse.status,
          headers: { "Content-Type": "application/json" },
        });
      }

      const responseData = await upstreamResponse.json();

      // 处理响应，检查是否包含工具调用
      if (responseData.choices && responseData.choices[0]?.message?.content) {
        const content = responseData.choices[0].message.content;
        const toolUses = this.parseToolUse(content);

        console.log('🔍 Found tool uses:', toolUses.length);
        
        if (toolUses.length > 0) {
          // 转换为OpenAI格式的tool_calls
          const toolCalls: ToolCall[] = toolUses.map(toolUse => ({
            id: this.generateToolCallId(),
            type: "function",
            function: {
              name: toolUse.toolName,
              arguments: toolUse.arguments
            }
          }));

          // 移除XML标签，保留其他内容
          let cleanContent = content;
          for (const toolUse of toolUses) {
            cleanContent = cleanContent.replace(toolUse.fullMatch, "").trim();
          }

          responseData.choices[0].message = {
            role: "assistant",
            content: cleanContent || null,
            tool_calls: toolCalls
          };
          
          console.log('✅ Converted to tool_calls:', toolCalls.map(tc => tc.function.name));
        }
      } else {
        console.log('⚠️  No content in response or empty content');
        if (responseData.choices && responseData.choices[0]) {
          console.log('📋 Message structure:', {
            role: responseData.choices[0].message?.role,
            hasContent: !!responseData.choices[0].message?.content,
            contentValue: responseData.choices[0].message?.content,
            hasToolCalls: !!responseData.choices[0].message?.tool_calls
          });
        }
      }

      console.log('📤 Returning response to client');
      return new Response(JSON.stringify(responseData), {
        headers: { "Content-Type": "application/json" },
      });

    } catch (error) {
      console.error("❌ Error processing request:", error);
      return new Response(JSON.stringify({
        error: {
          message: error instanceof Error ? error.message : "Internal server error",
          type: "internal_error",
          code: "internal_error"
        }
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  }

  /**
   * 处理模型列表请求
   */
  async handleModels(requestUrl: string, requestHeaders: Headers): Promise<Response> {
    try {
      // 从请求中解析上游URL和API密钥
      const upstreamUrl = this.parseUpstreamUrl(requestUrl);
      const apiKey = this.extractApiKey(requestHeaders);

      // 发送请求到上游服务
      const upstreamResponse = await fetch(`${upstreamUrl}/v1/models`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${apiKey}`,
        },
      });

      if (!upstreamResponse.ok) {
        const errorText = await upstreamResponse.text();
        return new Response(errorText, {
          status: upstreamResponse.status,
          headers: { "Content-Type": "application/json" },
        });
      }

      const responseData = await upstreamResponse.json();
      return new Response(JSON.stringify(responseData), {
        headers: { "Content-Type": "application/json" },
      });

    } catch (error) {
      return new Response(JSON.stringify({
        error: {
          message: error instanceof Error ? error.message : "Internal server error",
          type: "internal_error",
          code: "internal_error"
        }
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  }
}

// 主服务器逻辑
async function main() {
  const port = parseInt(Deno.env.get("PORT") || "8000");
  const proxy = new SimpleFunctionCallProxy();

  console.log(`🚀 Simple Function Call Proxy Server starting on port ${port}`);
  console.log(`📖 URL format: /{upstream_url}`);
  console.log(`📝 Examples:`);
  console.log(`   Chat: http://localhost:${port}/https://api.openai.com/v1/chat/completions`);
  console.log(`   Models: http://localhost:${port}/https://api.openai.com/v1/models`);

  await serve(async (req: Request) => {
    const url = new URL(req.url);
    
    // 处理CORS
    if (req.method === "OPTIONS") {
      return new Response(null, {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        },
      });
    }

    // 健康检查端点
    if (url.pathname === "/health" && req.method === "GET") {
      return new Response(JSON.stringify({
        status: "healthy",
        timestamp: new Date().toISOString(),
        version: "1.0.1"
      }), {
        headers: { 
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*"
        },
      });
    }

    // 处理chat/completions端点
    if (url.pathname.endsWith("/v1/chat/completions") && req.method === "POST") {
      try {
        const requestData = await req.json() as ChatCompletionRequest;
        const response = await proxy.handleChatCompletion(requestData, req.url, req.headers);

        // 添加CORS头
        const headers = new Headers(response.headers);
        headers.set("Access-Control-Allow-Origin", "*");

        return new Response(response.body, {
          status: response.status,
          headers,
        });
      } catch (error) {
        console.error("❌ Error parsing request:", error);
        return new Response(JSON.stringify({
          error: {
            message: "Invalid JSON in request body",
            type: "invalid_request_error",
            code: "invalid_json"
          }
        }), {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*"
          },
        });
      }
    }

    // 处理models端点
    if (url.pathname.endsWith("/v1/models") && req.method === "GET") {
      const response = await proxy.handleModels(req.url, req.headers);

      // 添加CORS头
      const headers = new Headers(response.headers);
      headers.set("Access-Control-Allow-Origin", "*");

      return new Response(response.body, {
        status: response.status,
        headers,
      });
    }

    // 返回使用说明
    if (url.pathname === "/" && req.method === "GET") {
      return new Response(JSON.stringify({
        name: "Simple OpenAI Function Call Proxy",
        version: "1.0.2",
        description: "Add function calling support to any OpenAI-compatible API",
        format: "/{upstream_url}",
        endpoints: {
          chat: `http://localhost:${port}/https://api.openai.com/v1/chat/completions`,
          models: `http://localhost:${port}/https://api.openai.com/v1/models`
        },
        usage: {
          authorization: "Bearer <your-api-key>",
          content_type: "application/json"
        }
      }), {
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*"
        },
      });
    }

    // 其他请求返回404
    return new Response(JSON.stringify({
      error: {
        message: "Not Found. Use format: /{upstream_url}",
        type: "not_found_error",
        code: "not_found"
      }
    }), { 
      status: 404,
      headers: { 
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*"
      }
    });
  }, { port });
}

if (import.meta.main) {
  main();
}
